# To build this by itself, use the make target esmf_time_f90_only 
# from the top-level WRF Makefile.  
# > cd ../..
# configure
# make esmf_time_f90_only
#
# Modified for WRF-ROMS ESMF-NUOPC Coupling (<PERSON><PERSON>/<PERSON>, Jan 2019)

.SUFFIXES: .F90 .o .f

# The "set" function gets rid of single quotes after comments
# (WARNING: This will break if a quoted string is followed by a comment that has
#           a single quote).
# Then, rename "ESMF" to "MYESMF" and "esmf" to "myesmf" to avoid conflicts when
# newer version of the ESMF/NUOPC libraries is used for coupling.

SED_FTN = sed -e "/\!.*'/s/'//g" -e "s/ESMF/MYESMF/g" -e "s/esmf/myesmf/g"

# Removes blanks lines generated by the C-preprocesor to facilate debugging with
# TotalView.

SED_BLK = sed -e '/^\s*$$/d'

# Redefine CPP macro by "-traditional-cpp -w" flags toavoid errors in continuation
# statements when compiling because syntax like "// &" is removed by the
# C-preprocessor.

MYCPP = $(CPP) -traditional-cpp -w

RM = /bin/rm -f
AR = ar
#RANLIB	= ranlib
RANLIB	= echo
FGREP = fgrep -iq

OBJS = MYESMF_Alarm.o MYESMF_BaseTime.o MYESMF_Clock.o MYESMF_Time.o \
        Meat.o MYESMF_Base.o MYESMF_Calendar.o MYESMF_Fraction.o   \
        MYESMF_TimeInterval.o MYESMF_Stubs.o MYESMF_Mod.o \
        module_symbols_util.o \
        module_utility.o MYESMF_AlarmClock.o

default: libmyesmf_time.a

tests: Test1_MYESMF.exe Test1_WRFU.exe

libmyesmf_time.a : $(OBJS)
	$(RM) libmyesmf_time.a
	if [ "$(AR)" != "lib.exe" ] ; then \
          $(AR) ru libmyesmf_time.a $(OBJS) ; \
        else \
          $(AR) /out:libmyesmf_time.a $(OBJS) ; \
        fi
	$(RANLIB) libmyesmf_time.a

Test1_MYESMF.f : Test1.F90
	$(RM) Test1_MYESMF.b Test1_MYESMF.bb Test1_MYESMF.f
	$(SED_FTN) Test1.F90 > Test1_MYESMF.b
	$(MYCPP) -I. Test1_MYESMF.b > Test1_MYESMF.bb
	$(SED_BLK) Test1_MYESMF.bb > Test1_MYESMF.f
	$(RM) Test1_MYESMF.b Test1_MYESMF.bb

Test1_MYESMF.exe : libmyesmf_time.a Test1_MYESMF.o
	$(FC) -o Test1_MYESMF.exe Test1_MYESMF.o libmyesmf_time.a

Test1_WRFU.f : Test1.F90
	$(RM) Test1_WRFU.b Test1_WRFU.bb Test1_WRFU.f
	sed -e "s/ESMF_Mod/module_utility/g" -e "s/ESMF_/WRFU_/g" Test1.F90 > Test1_WRFU.b
	$(MYCPP) -I. Test1_WRFU.b > Test1_WRFU.bb
	$(SED_BLK) Test1_WRFU.bb > Test1_WRFU.f
	$(RM) Test1_WRFU.b Test1_WRFU.bb

Test1_WRFU.exe : libmyesmf_time.a Test1_WRFU.o
	$(FC) -o Test1_WRFU.exe Test1_WRFU.o libmyesmf_time.a

.F90.o : MYESMF_TimeMgr.inc MYESMF_Macros.inc
	$(RM) $@
	$(SED_FTN) $*.F90 > $*.b
	$(MYCPP) -I. $*.b > $*.bb
	$(SED_BLK) $*.bb > $*.f
	$(RM) $*.b $*.bb
	@ if echo $(MYCPP) | $(FGREP) 'DVAR4D'; then \
          echo COMPILING $*.F90 for 4DVAR ; \
          $(WRF_SRC_ROOT_DIR)/var/build/da_name_space.pl $*.f > $*.f.tmp ; \
          mv -v $*.f.tmp $*.f ; \
        fi
	@ if echo $* | $(FGREP) 'ESMF'; then \
	  mv -v $*.f MY$*.f ; \
	fi
	$(FC) -o $@ -c $*.f

.F90.f : MYESMF_TimeMgr.inc MYESMF_Macros.inc
	$(RM) $@
	$(SED_FTN) $*.F90 > $*.b
	$(MYCPP) -I. $*.b > $*.bb
	$(SED_BLK) $*.bb > $*.f	
	$(RM) $*.b $*.bb
	@ if echo $(MYCPP) | $(FGREP) 'DVAR4D'; then \
          echo COMPILING $*.F90 for 4DVAR ; \
          $(WRF_SRC_ROOT_DIR)/var/build/da_name_space.pl $*.f > $*.f.tmp ; \
          mv -v $*.f.tmp $*.f ; \
        fi
	@ if echo $* | $(FGREP) 'ESMF'; then \
	  mv -v $*.f MY$*.f ;\
	fi

.f.o : MYESMF_TimeMgr.inc MYESMF_Macros.inc
	$(RM) $@
	$(RM) $*.b $*.bb
	$(FC) -c $*.f

MYESMF_TimeMgr.inc: ESMF_TimeMgr.inc
	$(SED_FTN) ESMF_TimeMgr.inc > MYESMF_TimeMgr.inc

MYESMF_Macros.inc: ESMF_Macros.inc
	$(SED_FTN) ESMF_Macros.inc > MYESMF_Macros.inc

clean : testclean

testclean: 
	$(RM) *.b *.bb *.f *.f90 *.o *.obj *.i MYESMF*.inc libmyesmf_time.a *.mod Test1*.exe

superclean: testclean
	$(RM) Test1*.out make_tests.out

# DEPENDENCIES : only dependencies after this line 

#$$$ update dependencies!  

ESMF_Alarm.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_AlarmClock.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_BaseTime.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_Calendar.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_Clock.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_Mod.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_Time.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
ESMF_TimeInterval.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc
Meat.f: MYESMF_TimeMgr.inc MYESMF_Macros.inc 

MYESMF_Alarm.f: ESMF_Alarm.f
MYESMF_AlarmClock.f: ESMF_AlarmClock.f
MYESMF_Base.f: ESMF_Base.f
MYESMF_BaseTime.f: ESMF_BaseTime.f
MYESMF_Calendar.f: ESMF_Calendar.f
MYESMF_Clock.f: ESMF_Clock.f
MYESMF_Fraction.f: ESMF_Fraction.f
MYESMF_Mod.f: ESMF_Mod.f
MYESMF_Stubs.f: ESMF_Stubs.f
MYESMF_Time.f: ESMF_Time.f
MYESMF_TimeInterval.f: ESMF_TimeInterval.f
MYESMF_TimeMgr.f: ESMF_TimeMgr.f

MYESMF_Alarm.o : MYESMF_Base.o MYESMF_Time.o MYESMF_TimeInterval.o
MYESMF_BaseTime.o : MYESMF_Base.o
MYESMF_Clock.o : MYESMF_Base.o MYESMF_Time.o MYESMF_TimeInterval.o MYESMF_Alarm.o
MYESMF_AlarmClock.o : MYESMF_Alarm.o MYESMF_Clock.o MYESMF_Time.o MYESMF_TimeInterval.o
MYESMF_Time.o : MYESMF_Base.o MYESMF_BaseTime.o MYESMF_TimeInterval.o MYESMF_Calendar.o \
              MYESMF_Stubs.o
MYESMF_Base.o :
MYESMF_Calendar.o : MYESMF_Base.o MYESMF_BaseTime.o
MYESMF_Fraction.o : 
MYESMF_TimeInterval.o : MYESMF_Base.o MYESMF_BaseTime.o MYESMF_Calendar.o MYESMF_Fraction.o
MYESMF_Mod.o : MYESMF_Alarm.o MYESMF_BaseTime.o MYESMF_Clock.o MYESMF_Time.o \
        MYESMF_Base.o MYESMF_Calendar.o MYESMF_Fraction.o    \
        MYESMF_TimeInterval.o MYESMF_Stubs.o MYESMF_AlarmClock.o
Meat.o : MYESMF_Alarm.o MYESMF_BaseTime.o MYESMF_Clock.o MYESMF_Time.o \
        MYESMF_Base.o MYESMF_Calendar.o MYESMF_Fraction.o    \
        MYESMF_TimeInterval.o MYESMF_TimeMgr.inc
MYESMF_Stubs.o : MYESMF_Base.o MYESMF_Calendar.o
module_utility.o :  module_symbols_util.o
module_symbols_util.o :  MYESMF_Mod.o
Test1.o :  module_utility.o



