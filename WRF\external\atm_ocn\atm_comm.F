      ! MODULE ATM_cc - 大气模块耦合通信变量定义模块
      !
      ! 该模块定义了大气模型(AM)与耦合器(Coupler)之间通信所需的变量和数据结构。
      ! 主要包含：
      !   - MPI通信相关参数
      !   - 耦合时间步长控制变量
      !   - 网格维度参数
      !   - 数据类型定义(REAL/INTEGER等)
      !   - 海表温度(SST)、表面通量(SF)、海流(ucur/vcur)、波浪状态(alpha/gamma)等数据结构
      !   - 耦合控制标志(ia2o,io2a,ia2w,iw2a等)
      !   - 各种不合理的低值阈值定义
      !
      ! 子程序功能：
      !   ATM_SET_COMM - 设置新的MPI通信域
      !   ATM_LEAVE_COUPLING - 处理器退出耦合通信
      !   ATM_CMP_START - 初始化耦合通信
      !   ATM_INIT_CHECK - 检查初始化状态
      !   ATM_TSTEP_INIT - 时间步初始化
      !   ATM_RECVdtc - 接收耦合时间步长
      !   ATM_SENDGRIDS - 发送网格信息
      !   ATM_SENDSLM - 发送海陆掩膜
      !   ATM_GETSST - 获取海表温度
      !   ATM_DOFLUXES - 处理表面通量
      !   ATM_SENDFLUXES - 发送表面通量
      !   ATM_ANNOUNCE - 调试信息输出
      !   ATM_GETCUR - 获取海洋流场
      !   ATM_GETWSTATE - 获取波浪状态
      !   ATM_PREPWINDP - 准备风场数据
      !   ATM_SENDWINDP - 发送风场数据
      !
      ! 注意：
      !   该模块是COAWST耦合系统的重要组成部分，负责大气模型与其他组件(海洋、波浪)的数据交换。
      MODULE ATM_cc

      ! 使用耦合通信模块，导入必要的变量和常数
      USE CMP_COMM, ONLY: &

     &   MPI_COMM_Atmos => COMM_local, &        ! 大气模型本地MPI通信域

     &   Coupler_id, &                          ! 耦合器进程ID
     &   component_master_rank_local, &         ! 组件主进程本地排名
     &   process_rank_local, &                  ! 进程本地排名
     &   component_nprocs, &                    ! 组件进程总数
     &   ibuffer, &                             ! 整数缓冲区

     &   MPI_INTEGER,MPI_STATUS_SIZE, &         ! MPI整数类型和状态大小
     &   kind_REAL,kind_alt_REAL, &             ! 实数精度类型定义
     &   MPI_kind_REAL,MPI_kind_alt_REAL        ! MPI实数类型定义

      implicit none

      ! 网格数量参数：最多支持3个嵌套网格
      integer,parameter:: ND=3
      ! 海洋模型规格和波浪模型ID初始化
      integer Ocean_spec /-1/, WM_id /-10/
      ! 表面通量变量数量
      integer NSF                               ! 海洋模型通量变量数
      integer NSF_WM                            ! 波浪模型通量变量数
      ! 时间步长相关变量
      real dtc,              &  !<- 耦合周期 (s)
     &     dta,              &  !<- 大气模型时间步长("物理"时间步) (s)
     &     dta2dtc              !<- 大气时间步长/耦合周期的比值
      integer i_dtc2dta /100/   !<- 耦合周期/大气时间步长的比值(整数)
      ! 网格维度参数
      integer & !,dimension(ND)::
     &ims,ime,jms,jme,its,ite,jts,jte,ids,idf,jds,jdf,  NGP
      ! ims,ime: 内存数组I方向起止索引
      ! jms,jme: 内存数组J方向起止索引
      ! its,ite: 瓦片I方向起止索引
      ! jts,jte: 瓦片J方向起止索引
      ! ids,idf: 域I方向起止索引
      ! jds,jdf: 域J方向起止索引
      ! NGP: 网格点总数
      integer kms,kme,kts,kte,kds,kde           ! 垂直方向维度参数
      ! 实数精度类型定义
      integer,parameter:: kind_R=kind_alt_REAL  ! 主要实数类型
!c     integer,parameter:: kind_tiling=kind_R   ! 瓦片实数类型(已注释)
      ! 各种物理量的数据精度类型定义
      integer,parameter:: kind_sfcflux=kind_R, &    ! 表面通量精度类型
     &                    kind_SST=kind_R, &         ! 海表温度精度类型
     &                    kind_SLM=kind_R, &         ! 海陆掩膜精度类型
     &                    kind_lonlat=kind_REAL      ! 经纬度精度类型
      ! 海洋流场、波浪状态和风场数据的精度类型定义
      INTEGER, PARAMETER  :: kind_cur = kind_r, &   ! 海洋流场精度类型
                             kind_wstate = kind_r, & ! 波浪状态精度类型
                             kind_windp = kind_r     ! 风场数据精度类型
      ! 对应的MPI数据类型变量
      integer MPI_kind_R, &                         ! 主要实数MPI类型
     &MPI_kind_sfcflux,MPI_kind_SST,MPI_kind_SLM,MPI_kind_lonlat
      INTEGER :: MPI_kind_cur, MPI_kind_wstate, MPI_kind_windp

      ! 时间步计数器数组(每个网格)和当前网格ID
      integer n_ts(ND) /0,0,0/, gid
      integer rc /5/                                ! 返回代码

      ! 物理常数和阈值定义
      real,parameter:: &
     &   SLM_OS_value=1., &                         !<- 大气模型中开放海域掩膜的实际值
     &   unrealistically_low_SST=0.01, &           ! <- 不合理的低海表温度值(>=0)
                                                    ! 用于插值检查
     &   unrealistically_low_SV=-1.E30, &          ! <- 不合理的低表面通量值(负值)
                                                    ! 或其他要发送给耦合器的表面值
                                                    ! 参见耦合器代码
     &   unrealistically_low_SF=unrealistically_low_SV, & !<- 同上
     &   unrealistically_low_SVp=0.99*unrealistically_low_SV ! <- 稍大的不合理低值

      ! 逻辑控制变量
      logical initialized /.false./                ! 初始化标志
      logical PHYS,zeroSF,nrmSF,sendSF,getSST      ! 物理过程和通量处理控制标志

      ! 海表温度数组类型定义
      TYPE SST_ARRAY
        real(kind=kind_SST),dimension(:,:),pointer:: a  ! 二维海表温度指针数组
      END TYPE SST_ARRAY

      ! 表面通量数组类型定义
      TYPE SF_ARRAY
        real(kind=kind_sfcflux),dimension(:,:,:),pointer:: a  ! 三维表面通量指针数组
      END TYPE SF_ARRAY

      ! 海洋流场数组类型定义
      TYPE cur_array
        REAL(KIND = kind_cur), dimension(:,:), pointer :: a  ! 二维海洋流场指针数组
      END TYPE cur_array

      ! 波浪状态数组类型定义
      TYPE wstate_array
        REAL(KIND = kind_wstate), dimension(:,:), pointer :: a  ! 二维波浪状态指针数组
      END TYPE wstate_array

      ! 风场数据数组类型定义
      TYPE windp_array
        REAL(KIND = kind_windp), dimension(:,:,:), pointer :: a  ! 三维风场数据指针数组
      END TYPE windp_array

      ! 各种数据数组的实例声明
      TYPE (SST_ARRAY), dimension(ND):: SST_cc           ! 海表温度数组(每个网格)
      TYPE (SF_ARRAY), dimension(min(ND,2)):: sf         ! 表面通量数组(前两个网格)
      TYPE (cur_array), dimension(nd) ::  ucur_cc, vcur_cc  ! X,Y方向海洋流场数组
      TYPE (wstate_array), dimension(nd) :: alpha_cc, gamma_cc  ! Charnok系数和风浪夹角数组
      TYPE (windp_array), dimension(nd) :: wwinp         ! 风场输入数据数组

      character*12 sgid                                  ! 网格ID字符串

!控制变量:
      integer nunit_announce /6/, VerbLev /3/            ! 输出单元号和详细级别
! 控制大气-海洋-波浪耦合的标志
      integer ia2o /1/, &                               ! 大气到海洋耦合标志
     &        io2a /1/, &                               ! 海洋到大气耦合标志
     &        ia2w /1/, &                               ! 大气到波浪耦合标志
     &        iw2a /0/                                  ! 波浪到大气耦合标志

      SAVE                                              ! 保存所有模块变量

      END MODULE ATM_cc
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_SET_COMM(new_atm_comm)
        USE ATM_cc
        integer, intent(in) :: new_atm_comm

!       This routine is called when the atmospheric model wants to
!       remove processors from taking part in coupling so that they
!       can perform I/O or diagnostics.  Any processors that will
!       continue to be in MPI_COMM_Atmos must call this routine, and
!       any processors that will leave MPI_COMM_Atmos must call
!       ATM_LEAVE_COUPLING.

        MPI_COMM_Atmos=new_atm_comm
      end
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_LEAVE_COUPLING()
        USE ATM_cc

!       This routine is called when the atmospheric model wants to
!       remove processors from taking part in coupling so that they
!       can perform I/O or diagnostics.  Any processors that will
!       continue to be in MPI_COMM_Atmos must call ATM_SET_COMM, and
!       any processors that will leave MPI_COMM_Atmos must call
!       this routine.

!       Currently, there is nothing we have to do here.
        return

      end
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_CMP_START(atm_comm)

      USE ATM_cc

      implicit none

      integer atm_comm

!     integer Atmos_id /1/, Atmos_master_rank_local /0/, Atmos_spec /1/
      integer Atmos_id /1/, Atmos_master_rank_local /0/, Atmos_spec /0/
!                                                            <- D.S.
!        Atmos_spec=1 for the case when the only field AM receives
!        from Coupler is SST. Atmos_spec=0 allows receiving additional
!        fields from C., originating from both OM, WM. (Atmos_spec does
!        not control receiving in AM but is sent to C. thus transferring
!        the control to C.)
      integer ibuf(1),ierr
      character*20 s
!C

                      !<-id of OM as a component of the coupled system
      call CMP_INIT(Atmos_id,1)
                             !<-"flexibility level"
      if (Coupler_id.ge.0) VerbLev=min(VerbLev,ibuffer(4))
      write(s,'(i2)') VerbLev

      call CMP_INTRO(Atmos_master_rank_local)
      call ATM_ANNOUNCE('back from CMP_INTRO, VerbLev='//s,2)

      initialized=.true.

      call CMP_INTEGER_SEND(Atmos_spec,1)

      call CMP_gnr_RECV(Ocean_spec,1,MPI_INTEGER)
      write(s,'(i2)') Ocean_spec
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, OM spec is '//s,2)
      call MPI_BCAST(Ocean_spec,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: Ocean_spec broadcast',2)

      call CMP_gnr_RECV(WM_id,1,MPI_INTEGER)
      write(s,'(i4)') WM_id
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, WM id is '//s,2)
      call MPI_BCAST(WM_id,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: WM_id broadcast',2)
      if (WM_id.gt.0) then
        NSF_WM=4
      else
        NSF_WM=0
      end if

      if (Ocean_spec.eq.1) then
        NSF=4
      else if (Ocean_spec.eq.2) then
        NSF=8
      else if (Ocean_spec.eq.0) then
        NSF=1
      else if (Coupler_id.ge.0) then
        call GLOB_ABORT(Ocean_spec-1, &
     &  'ATM_CMP_START received wrong Ocean_spec value, aborted',rc)
      else
        Ocean_spec=1
        NSF=4
        call ATM_ANNOUNCE('AM is standalone: Ocean_spec=1, NSF=4'// &
     &  ' assigned (as if for POM coupling)',2)
      end if

      call CMP_gnr_RECV(ia2o,1,MPI_INTEGER)
      write(s,'(i4)') ia2o
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, ia2o is '//s,2)
      call MPI_BCAST(ia2o,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: ia2o broadcast',2)

      call CMP_gnr_RECV(io2a,1,MPI_INTEGER)
      write(s,'(i4)') io2a
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, io2a is '//s,2)
      call MPI_BCAST(io2a,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: io2a broadcast',2)

      call CMP_gnr_RECV(ia2w,1,MPI_INTEGER)
      write(s,'(i4)') ia2w
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, ia2w is '//s,2)
      call MPI_BCAST(ia2w,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: ia2w broadcast',2)

      call CMP_gnr_RECV(iw2a,1,MPI_INTEGER)
      write(s,'(i4)') iw2a
      call ATM_ANNOUNCE('back from CMP_INTEGER_RECV, iw2a is '//s,2)
      call MPI_BCAST(iw2a,1,MPI_INTEGER, &
     &component_master_rank_local,MPI_COMM_Atmos,ierr)
      call ATM_ANNOUNCE('ATM_CMP_START: iw2a broadcast',2)

      if (kind_R.eq.kind_REAL) then
        MPI_kind_R=MPI_kind_REAL
      else 
        MPI_kind_R=MPI_kind_alt_REAL
      end if
      if (kind_sfcflux.eq.kind_REAL) then
        MPI_kind_sfcflux=MPI_kind_REAL
      else 
        MPI_kind_sfcflux=MPI_kind_alt_REAL
      end if
      if (kind_SST.eq.kind_REAL) then
        MPI_kind_SST=MPI_kind_REAL
      else 
        MPI_kind_SST=MPI_kind_alt_REAL
      end if
      if (kind_SLM.eq.kind_REAL) then
        MPI_kind_SLM=MPI_kind_REAL
      else 
        MPI_kind_SLM=MPI_kind_alt_REAL
      end if
      if (kind_lonlat.eq.kind_REAL) then
        MPI_kind_lonlat=MPI_kind_REAL
      else 
        MPI_kind_lonlat=MPI_kind_alt_REAL
      end if

      IF (kind_cur == kind_real) THEN
         MPI_kind_cur = MPI_kind_real
      ELSE
         MPI_kind_cur = MPI_kind_alt_real
      END IF
      IF (kind_wstate == kind_real) THEN
         MPI_kind_wstate = MPI_kind_real
      ELSE
         MPI_kind_wstate = MPI_kind_alt_real
      END IF
      IF (kind_windp == kind_real) THEN
         MPI_kind_windp = MPI_kind_real
      ELSE
         MPI_kind_windp = MPI_kind_alt_real
      END IF

      atm_comm=MPI_COMM_Atmos

      return
      END
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_INIT_CHECK(s)

      USE ATM_cc, ONLY: initialized,rc

      implicit none

      character*(*) s

      if (.not. initialized) call GLOB_ABORT(1,s,rc)

      return
      END
!C
!C***********************************************************************
!C
      subroutine ATM_TSTEP_INIT(NTSD,NPHS,gid_,dta_, &
     &ids_,idf_,jds_,jdf_,its_,ite_,jts_,jte_,ims_,ime_,jms_,jme_, &
       !<-"domain"         !<-"tile"           !<-"memory" (tile+halo)
     &kds_,kde_,kts_,kte_,kms_,kme_,&
     &HLON,HLAT,VLON,VLAT, &
     &SLM, &
     &i_parent_start,j_parent_start,&
     &guessdtc,dtc_)

      USE ATM_cc

      implicit none

      real, intent(in) :: guessdtc
      real, intent(out) :: dtc_

      integer NTSD,NPHS,gid_
      real dta_
      integer ids_,idf_,jds_,jdf_,its_,ite_,jts_,jte_, &
     &ims_,ime_,jms_,jme_,kds_,kde_,kts_,kte_,kms_,kme_
      real(kind=kind_lonlat),dimension(ims_:ime_,jms_:jme_):: &
     &HLON,HLAT,VLON,VLAT
      real(kind=kind_SLM),dimension(ims_:ime_,jms_:jme_):: SLM
      integer i_parent_start,j_parent_start

      integer KDT,buf(2) /0,0/
      character*24 s
      character*80 s1
      character*255 message
      SAVE
!C

      gid=gid_
      call GLOB_ABORT((gid-1)*(gid-2)*(gid-3), &
     &'Abort: in ATM_TSTEP_INIT gid is neither 1 nor 2 nor 3',rc)
      KDT=NTSD/NPHS+1
      PHYS=MOD(NTSD,NPHS).eq.0 ! .and. gid.eq.1 <-removed to bring MG in
      dta=dta_ 

      write(s1,'("gid=",i1," NTSD=",i5," NPHS=",i3," KDT=",i5,'// &
     &'" PHYS=",L1)') gid,NTSD,NPHS,KDT,PHYS
      call ATM_ANNOUNCE('ATM_TSTEP_INIT entered: '//trim(s1),3)

!c     IF (n_ts.eq.-1 .and. PHYS) THEN
!c       PHYS=.false.
!c       n_ts=0   ! init. value must be -1 . But if PHYS does not need
!c                ! this correction, init. value must be 0 (whereas this
!c                ! IF statement may stay)
!c     END IF
      if (.not.PHYS) then
        zeroSF=.false.
        nrmSF=.false.
        sendSF=.false.
        RETURN
      end if

      n_ts(gid)=n_ts(gid)+1  ! init. value must be 0   ***0***
      write(s,'(2i8)') KDT,n_ts(gid)
      write(sgid,'(" grid id = ",i1)') gid
      call ATM_ANNOUNCE('ATM_TSTEP_INIT working:'// &
     &sgid//'; KDT, n_ts: '//s,3)
      call GLOB_ABORT(KDT-n_ts(gid), &
     &'Abort: in ATM_TSTEP_INIT KDT, n_ts(gid) differ '//s,rc)

      call ATM_RECVdtc(guessdtc)
      dtc_=dtc

      zeroSF=((n_ts(gid)-1)/i_dtc2dta)*i_dtc2dta .eq. n_ts(gid)-1
      nrmSF=(n_ts(gid)/i_dtc2dta)*i_dtc2dta .eq. n_ts(gid)
      sendSF=(n_ts(gid)/i_dtc2dta)*i_dtc2dta .eq. n_ts(gid)
                                    !<-check, this depends
                                    ! on where ATM_SENDFLUXES is called.
                                    ! MOD(n_ts,i_dtc2dta).eq.0 should
                                    ! be good for calling it after
                                    ! ATM_DOFLUXES at the same t.s.

      ids=ids_
      idf=idf_
      jds=jds_
      jdf=jdf_
      its=its_
      ite=ite_
      jts=jts_
      jte=jte_
      ims=ims_
      ime=ime_
      jms=jms_
      jme=jme_

      kds=kds_
      kde=kde_
      kts=kts_
      kms=kms_
      kme=kme_
      kte=kte_

      NGP=(idf-ids+1)*(jdf-jds+1)

      IF (n_ts(gid).eq.1) THEN

      call ATM_ANNOUNCE('ATM_TSTEP_INIT to allocate sf, SST_cc',3)

      IF (gid.le.2) THEN !** innermost grid not active in coupling **
        allocate(sf(gid)%a(ims:ime,jms:jme,NSF))
        ALLOCATE(wwinp(gid)%a(ims:ime,jms:jme,NSF_WM))
      END IF !** innermost grid not active in coupling **
        allocate(SST_cc(gid)%a(ims:ime,jms:jme))
      ALLOCATE(ucur_cc(gid)%a(ims:ime,jms:jme), vcur_cc(gid)%a(ims:ime,jms:jme))
      ALLOCATE(alpha_cc(gid)%a(ims:ime,jms:jme), gamma_cc(gid)%a(ims:ime,jms:jme))

      END IF

      if (gid.eq.2) then
        write(s,'(2i8)') i_parent_start,j_parent_start
        if (zeroSF) then
          buf(1)=i_parent_start
          buf(2)=j_parent_start
          call CMP_INTEGER_SEND(buf,2)
          call ATM_ANNOUNCE( &
     &    'ATM_TSTEP_INIT: i_parent_start, j_parent_start sent '//s,3)
        else
          call GLOB_ABORT(abs(i_parent_start-buf(1))+abs(j_parent_start- &
     &    buf(2)),'NESTED GRID MOVED DURING C TIME STEP: ABORTED '// &
     &    s,rc)
        end if
      end if

      IF (gid.le.2) THEN !** innermost grid not active in coupling **

      CALL ATM_SENDGRIDS(HLON,HLAT,VLON,VLAT)

      CALL ATM_SENDSLM(SLM)

      END IF !** innermost grid not active in coupling **

      if (VerbLev.ge.2) then
         write(message,*) 'AM: ATM_TSTEP_INIT: returning ',gid,         &
     &n_ts(gid),ids,idf,jds,jdf,its,ite,jts,jte,ims,ime,jms,jme,NGP,NSF
         call wrf_debug(2,message)
      endif

      RETURN
      end
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_RECVdtc(guessdtc)

      USE ATM_cc

      implicit none

      real,intent(in) :: guessdtc
      real(kind=kind_R) buf(1),dtc2dta
      integer ierr,i
      logical first/.true./
      character*20 s
      SAVE
!C

      write(s,'(1pe20.12)') dta
      call ATM_ANNOUNCE('ATM_RECVdtc: AM time step dta='//s,3)

      IF (first) THEN
        call ATM_ANNOUNCE( &
     &  'ATM_RECVdtc: to receive C time step; AM time step dta='//s,2)

        call CMP_gnr_RECV(buf,1,MPI_kind_R)

        call MPI_BCAST(buf,1,MPI_kind_R, &
     &  component_master_rank_local,MPI_COMM_Atmos,ierr)
        call ATM_ANNOUNCE('ATM_RECVdtc: C time step broadcast',2)
        dtc=buf(1)

        if (Coupler_id.lt.0) then
          dtc=guessdtc
          write(s,'(1pe20.12)') dtc
          call ATM_ANNOUNCE('ATM_RECVdtc: C time step assigned '// &
     &    trim(s)//' : standalone mode',2)
        else
          write(s,'(1pe20.12)') buf
          call ATM_ANNOUNCE( &
     &    'ATM_RECVdtc: C time step dtc='//s//' received',2)
        end if
      END IF

      dtc2dta=dtc/dta
      i_dtc2dta=nint(dtc2dta)
      if (abs(i_dtc2dta-dtc2dta).gt.1.E-5) call GLOB_ABORT(1, &
     &'AM: ABORTED: dtc is not a multiple of dta',1)

      i=3
      if (n_ts(gid).eq.1) i=2
      if (i_dtc2dta.eq.0) then
        i_dtc2dta=4
        call ATM_ANNOUNCE('ratio of C/AM time steps =0, assigned 4 .'// &
     &  ' This should only occur in standalone mode and ONLY IF dtc '// &
     &  'HAS NOT BEEN ASSIGNED A POSITIVE VALUE: ** ATTENTION **',i)
      else
        write(s,'(i2)') i_dtc2dta
        call ATM_ANNOUNCE('ratio of C/AM time steps: '//trim(s),i)
      end if

      dta2dtc=1./i_dtc2dta

      first=.false.

      RETURN
      END
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_SENDGRIDS(HLON,HLAT,VLON,VLAT)

      USE ATM_cc

      implicit none

      real(kind=kind_lonlat),dimension(ims:ime,jms:jme):: &
     &HLON,HLAT,VLON,VLAT 

      real(kind=kind_lonlat),dimension(ids:idf,jds:jdf):: &
     &ALONt,ALATt,ALONv,ALATv

      integer buf(2)
!C

!c     IF (gid.ne.1) RETURN ! only "parent grid" dim. and coor. are sent

      IF (.not.PHYS .or. n_ts(gid).ne.1) RETURN

      IF (gid.gt.2) RETURN ! innermost grid's dim. / coor. are not sent
      
!temporarily excluded      if (Coupler_id.lt.0) return    !   <- standalone mode

      buf(1)=idf-ids+1
      buf(2)=jdf-jds+1
      call ATM_ANNOUNCE('to send grid dimensions,'//sgid,1)
      call CMP_INTEGER_SEND(buf,2)
      call ATM_ANNOUNCE('grid dimensions sent,'//sgid,1)

!c     IF (gid.eq.1) THEN    !  only "parent grid" coordinates are sent

        if (kind_lonlat.eq.4) then
          call ASSEMBLE(ALONt,HLON,kind_lonlat)
          call ASSEMBLE(ALATt,HLAT,kind_lonlat)
          call ASSEMBLE(ALONv,VLON,kind_lonlat)
          call ASSEMBLE(ALATv,VLAT,kind_lonlat)
        else if (kind_lonlat.eq.8) then
          call ASSEMBLE_R8(ALONt,HLON,kind_lonlat)
          call ASSEMBLE_R8(ALATt,HLAT,kind_lonlat)
          call ASSEMBLE_R8(ALONv,VLON,kind_lonlat)
          call ASSEMBLE_R8(ALATv,VLAT,kind_lonlat)
        else
          call GLOB_ABORT(1,'wrong value of kind_lonlat in ATM_SENDGRIDS',1)
        end if

        call ATM_ANNOUNCE('(BP) to send grid arrays (4 MPI calls)',2)

        call CMP_gnr_SEND(ALONt,NGP,MPI_kind_lonlat)
        call CMP_gnr_SEND(ALATt,NGP,MPI_kind_lonlat)
        call CMP_gnr_SEND(ALONv,NGP,MPI_kind_lonlat)
        call CMP_gnr_SEND(ALATv,NGP,MPI_kind_lonlat)

        call ATM_ANNOUNCE('the 4 grid arrays sent',1)

!c     END IF

      call ATM_ANNOUNCE('(BP) ATM_SENDGRIDS: returning',2)

      return
      END
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_SENDSLM(SLM)

      USE ATM_cc

      implicit none

      real(kind=kind_SLM),dimension(ims:ime,jms:jme):: SLM

      real(kind=kind_SLM),dimension(ids:idf,jds:jdf):: SLM_g
      integer buf(2)
!C

!c     IF (gid.ne.1) RETURN  !  only "parent grid" mask is sent
      IF (.not.PHYS .or. n_ts(gid).ne.1) RETURN

      IF (gid.gt.2) RETURN ! innermost grid's mask is not sent
      
!temporarily excluded      if (Coupler_id.lt.0) return    !   <- standalone mode

      call ASSEMBLE(SLM_g,SLM,kind_SLM)

      call ATM_ANNOUNCE('(BP) to send SLM',2)

      call CMP_gnr_SEND(SLM_g,NGP,MPI_kind_SLM)
      call CMP_gnr_SEND(SLM_g,NGP,MPI_kind_SLM)
           ! Coupler requires as many copies of mask as there are grids
           ! [and mask array is the same for H- (=t-) and V- grids]

      call ATM_ANNOUNCE('(BP) ATM_SENDSLM: returning',2)

      return
      END
!C
!C***********************************************************************
!C
      SUBROUTINE ATM_GETSST(SST,SLM)

      USE ATM_cc

      implicit none

      real(kind=kind_SST) SST(ims:ime,jms:jme)
      real(kind=kind_SLM) SLM(ims:ime,jms:jme)

      integer i,j
      real(kind=kind_SST) SST_g(ids:idf,jds:jdf)
!C
      IF ( io2a .LT. 1 ) RETURN

      IF (.not.PHYS) RETURN

      IF (gid.gt.2) RETURN ! nothing is done to get innermost grid's
                           ! SST ** IN THIS PRELIMINARY VERSION **

      call ATM_ANNOUNCE('ATM_GETSST entered (PHYS=.true.)',3)

      getSST=((n_ts(gid)-1)/i_dtc2dta)*i_dtc2dta .eq. n_ts(gid)-1
      if (getSST.neqv.zeroSF) then
        call GLOB_ABORT(1,'getSST differs from zeroSF, which screws'// &
     &  ' up the design for exchanges with C',rc)
      end if

      if (getSST) then
        if (n_ts(gid).eq.1 .and. gid.eq.1) then
          call ATM_ANNOUNCE('ATM_GETSST: to send ref. SST'//sgid,2)
          call ASSEMBLE(SST_g,SST,kind_SST)
          call CMP_gnr_SEND(SST_g,NGP,MPI_kind_SST)
          call ATM_ANNOUNCE('ATM_GETSST: ref. SST sent'//sgid,2)
        end if
        call ATM_ANNOUNCE('ATM_GETSST: to receive SST',3)
        call CMP_gnr_RECV(SST_g,NGP,MPI_kind_SST)
        call DISASSEMBLE(SST_g,SST_cc(gid)%a,kind_SST)
        call ATM_ANNOUNCE('ATM_GETSST: SST received',3)
      end if
      
      if (Coupler_id.lt.0) return    !   <- standalone mode

      do j=jts,jte
      do i=its,ite
        if (abs(SLM(i,j)-SLM_OS_value).lt.0.01) then 
                                  ! i.e. if it is OS (open sea) AMGP
                                  !
          if (SST_cc(gid)%a(i,j).gt.unrealistically_low_SST) &
                                          ! i.e. if there is a valid
                                          ! result of interpolation from
                                          ! OMG for this AMGP
     &       SST(i,j)=SST_cc(gid)%a(i,j)
        end if
      end do
      end do

      return
      END
!C
!C***********************************************************************
!C
      ! ATM_DOFLUXES - 大气表面通量处理子程序
      ! 功能：处理和累积大气模型计算的表面通量，为耦合器传输做准备
      ! 输入参数：
      !   TWBS   - 感热通量 (W/m²)，向上为正
      !   QWBS   - 潜热通量 (W/m²)，向上为正
      !   RLWIN  - 向下长波辐射 (W/m²)
      !   RSWIN  - 向下短波辐射 (W/m²)
      !   RADOT  - 向上长波辐射 (W/m²)
      !   RSWOUT - 向上短波辐射 (W/m²)
      !   TX     - X方向风应力 (N/m²)
      !   TY     - Y方向风应力 (N/m²)
      !   PINT   - 界面气压 (Pa)，三维数组
      !   PREC   - 降水率 (kg/m²/s)
      SUBROUTINE ATM_DOFLUXES(TWBS,QWBS,RLWIN,RSWIN,RADOT,RSWOUT, &
!c    &USTAR,U10,V10,PINT,PREC)
     &TX,TY,PINT,PREC)

      USE ATM_cc                    ! 使用大气耦合通信模块

      implicit none

      ! 界面气压数组 - 三维数组(i,j,k)，单位：Pa
      real(kind=kind_sfcflux),dimension(ims:ime,jms:jme,kms:kme):: PINT

      ! 表面通量变量声明 - 二维数组(i,j)
      real(kind=kind_sfcflux),dimension(ims:ime,jms:jme):: &
     &TWBS,QWBS,RLWIN,RSWIN,RADOT,RSWOUT,TX,TY,PREC
!c    &TWBS,QWBS,RLWIN,RSWIN,RADOT,RSWOUT,USTAR,U10,V10,PINT,PREC
!       注意：PINT实际参数是三维数组 - 所以只有当Ps=实际参数(:,:,1)时才正确
!       实际上，Ps=PINT(:,1,:)

      ! 局部变量声明
      real(kind=kind_sfcflux),dimension(ims:ime,jms:jme):: SWR,R  ! SWR-净短波辐射，R-净辐射
      real dtainv                   ! 大气时间步长的倒数 (1/s)
!C

      ! 检查耦合控制标志：如果既不向海洋也不向波浪模型发送数据，则返回
      IF ( ia2o .LT. 1 .and. ia2w .LT. 1 ) RETURN
      ! 检查是否为物理时间步：如果不是物理时间步，则返回
      IF (.not.PHYS) RETURN

      ! 检查网格ID：只处理前两个网格(父网格和第一层嵌套网格)
      IF (gid.gt.2) RETURN

! 调试代码插入：->
!c     if (PREC(ims+3,jms+3).ne.0 .or. PREC(ims+5,jms+5).ne.0) then
!c       print*,'ATM_DOFLUXES,gid,n_ts(gid),PREC(3,3),PREC(5,5): ',
!c    &  gid,n_ts(gid),PREC(ims+3,jms+3),PREC(ims+5,jms+5)
!c     end if
! <-:调试代码插入结束

      ! 输出调试信息：进入ATM_DOFLUXES子程序
      call ATM_ANNOUNCE('ATM_DOFLUXES entered',3)

      ! 计算大气时间步长的倒数，用于时间平均
      dtainv=1./dta

      ! 如果是耦合时间步的开始，将表面通量数组清零
      if (zeroSF) sf(gid)%a=0.

      ! 计算净短波辐射：向上短波 - 向下短波 (向上为正的符号约定)
      SWR(its:ite,jts:jte)=-RSWIN(its:ite,jts:jte)+RSWOUT(its:ite,jts:jte)          ! 检查符号！这里SWR定义为向上为正
                                 ! 向上为正的符号约定
!c     sf(gid)%a(:,:,NSF-1)=sf(gid)%a(:,:,NSF-1)-TX
!c     sf(gid)%a(:,:,NSF)=sf(gid)%a(:,:,NSF)-TY
!c                    ! <- 应力分量的符号被改变
!c                    ! 所以这是-应力

!c     R=SWR+RADOT-RLWIN          ! 检查符号！这里R(净辐射)
                                 ! 定义为向上为正

!oooooooooooooooooooooooooooooo
      ! 海洋规格1：简化的4变量通量传输(适用于POM等简单海洋模型)
      IF (Ocean_spec.eq.1) THEN
!oooooooooooooooooooooooooooooo
        ! 累积净热通量 = -(感热+潜热) + 向上长波 - 向下长波 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-3)=sf(gid)%a(its:ite,jts:jte,NSF-3)-TWBS(its:ite,jts:jte)-QWBS(its:ite,jts:jte)+RADOT(its:ite,jts:jte)-RLWIN(its:ite,jts:jte)
                                       ! -TWBS (-QWBS) 应该是感热(潜热)通量，
                                       ! 向上为正的符号约定
        ! 累积净短波辐射通量 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-2)=sf(gid)%a(its:ite,jts:jte,NSF-2)+SWR(its:ite,jts:jte)
        ! 累积X方向风应力，符号取反 (N/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-1)=sf(gid)%a(its:ite,jts:jte,NSF-1)-TX(its:ite,jts:jte)
        ! 累积Y方向风应力，符号取反 (N/m²)
        sf(gid)%a(its:ite,jts:jte,NSF)=sf(gid)%a(its:ite,jts:jte,NSF)-TY(its:ite,jts:jte)
                     ! <- 应力分量的符号被改变
!ooooooooooooooooooooooooooooooooooo
      ! 海洋规格2：完整的8变量通量传输(适用于ROMS等复杂海洋模型)
      ELSE IF (Ocean_spec.eq.2) THEN
!ooooooooooooooooooooooooooooooooooo
        ! 累积降水率 (kg/m²/s)
        sf(gid)%a(its:ite,jts:jte,NSF-7)=sf(gid)%a(its:ite,jts:jte,NSF-7)+PREC(its:ite,jts:jte)
        ! 累积感热通量，符号取反 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-6)=sf(gid)%a(its:ite,jts:jte,NSF-6)-TWBS(its:ite,jts:jte)
        ! 累积潜热通量，符号取反 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-5)=sf(gid)%a(its:ite,jts:jte,NSF-5)-QWBS(its:ite,jts:jte)
        ! 累积海平面气压异常，减去标准大气压101300 Pa
        sf(gid)%a(its:ite,jts:jte,NSF-4)=sf(gid)%a(its:ite,jts:jte,NSF-4)+PINT(its:ite,jts:jte,1)-101300.
        ! 累积净长波辐射通量 = -净短波 - 向上长波 + 向下长波 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-3)=sf(gid)%a(its:ite,jts:jte,NSF-3)-SWR(its:ite,jts:jte)-RADOT(its:ite,jts:jte)+RLWIN(its:ite,jts:jte)
        ! 累积净短波辐射通量，符号取反 (W/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-2)=sf(gid)%a(its:ite,jts:jte,NSF-2)-SWR(its:ite,jts:jte)

        ! 累积X方向风应力，符号不变 (N/m²)
        sf(gid)%a(its:ite,jts:jte,NSF-1)=sf(gid)%a(its:ite,jts:jte,NSF-1)+TX(its:ite,jts:jte)
        ! 累积Y方向风应力，符号不变 (N/m²)
        sf(gid)%a(its:ite,jts:jte,NSF)=sf(gid)%a(its:ite,jts:jte,NSF)+TY(its:ite,jts:jte)
                     ! <- 应力分量的符号不改变
        ! 如果需要标准化表面通量
        if (nrmSF) then
          ! 将降水率乘以时间步长倒数，转换为m/s单位
          sf(gid)%a(its:ite,jts:jte,1)=sf(gid)%a(its:ite,jts:jte,1)*dtainv
                        ! 这样结果将是m/s；检查海洋模型需要什么单位
        end if
!ooooooooooo
      END IF
!ooooooooooo

      ! 如果需要标准化表面通量(在耦合时间步结束时)
      if (nrmSF) then
        ! 将累积的通量乘以时间比例因子，得到耦合时间步的平均值
        sf(gid)%a=sf(gid)%a*dta2dtc
      end if

      ! 输出调试信息：准备从ATM_DOFLUXES子程序返回
      call ATM_ANNOUNCE('ATM_DOFLUXES to return',3)

      return
      END
!C
!C***********************************************************************
!C
      ! ATM_SENDFLUXES - 发送表面通量数据到耦合器子程序
      ! 功能：将累积的表面通量数据组装并发送给耦合器，用于传输给海洋/波浪模型
      ! 无输入参数，使用模块变量sf(gid)%a中存储的通量数据
      SUBROUTINE ATM_SENDFLUXES

      USE ATM_cc                    ! 使用大气耦合通信模块

      implicit none

      ! 局部变量声明
      real(kind=kind_sfcflux) F(ids:idf,jds:jdf)  ! 组装后的全域通量数组
      integer n                     ! 通量变量循环计数器
!C

      ! 检查耦合控制标志：如果既不向海洋也不向波浪模型发送数据，则返回
      IF ( ia2o .LT. 1 .and. ia2w .LT. 1 ) RETURN
      ! 检查是否为物理时间步：如果不是物理时间步，则返回
      if (.not.PHYS) RETURN

      ! 检查网格ID：只处理前两个网格(父网格和第一层嵌套网格)
      IF (gid.gt.2) RETURN

      ! 检查是否需要发送表面通量：如果不是发送时间步，则返回
      if (.not.sendSF) then
        call ATM_ANNOUNCE( &
     &  'ATM_SENDLUXES entered with PHYS but not sendSF: returning'// &
     &  sgid,3)
        RETURN
      end if

      ! 输出调试信息：进入ATM_SENDFLUXES子程序
      call ATM_ANNOUNCE('In ATM_SENDLUXES'//sgid,3)

      ! 循环发送所有表面通量变量
      do n=1,NSF
        ! 将分布式数组组装成全域数组
        call ASSEMBLE(F,sf(gid)%a(:,:,n),kind_sfcflux)
        ! 通过耦合器发送通量数据
        call CMP_gnr_SEND(F,NGP,MPI_kind_sfcflux)
      end do

      ! 输出调试信息：准备从ATM_SENDFLUXES子程序返回
      call ATM_ANNOUNCE('ATM_SENDFLUXES to return'//sgid,3)

      return
      END
!C
!C***********************************************************************
!C
      ! ATM_ANNOUNCE - 大气模型调试信息输出子程序
      ! 功能：根据调试级别输出调试信息，支持MPI同步输出
      ! 输入参数：
      !   s      - 要输出的调试信息字符串
      !   DbgLev - 调试级别，只有当DbgLev <= VerbLev时才输出
      SUBROUTINE ATM_ANNOUNCE(s,DbgLev)

      USE ATM_cc, ONLY: nunit_announce,VerbLev,MPI_COMM_Atmos

      implicit none

      character*(*) s               ! 调试信息字符串
      integer DbgLev                ! 当前消息的调试级别

      integer ierr                  ! MPI错误代码
!C
      ! 检查调试级别：只有当消息级别不超过设定的详细级别时才输出
      if (DbgLev.le.VerbLev) then
        ! 如果消息以'(BP) '开头，表示需要MPI屏障同步
        if (s(1:5).eq.'(BP) ') then
          ! 执行MPI屏障，确保所有进程同步后再输出
          call MPI_BARRIER(MPI_COMM_Atmos,ierr)
        end if
        ! 调用通用输出函数，在消息前加上'AM: '前缀
        CALL CMP_ANNOUNCE(nunit_announce,'AM: '//s)
      end if

      return
      END

! ATM_GETCUR - 获取海洋表面流场子程序
! 功能：从耦合器接收海洋表面流场数据，并进行质量控制
! 作者：Biju Thomas, GSO/URI, 2015年4月8日
! 输出参数：
!   ucur - X方向海洋表面流速 (m/s)
!   vcur - Y方向海洋表面流速 (m/s)
SUBROUTINE atm_getcur(ucur,vcur)

! 获取海洋流场数据
! Biju Thomas,  GSO/URI  on 4/8/2015
!
   USE atm_cc                     ! 使用大气耦合通信模块
   IMPLICIT NONE

   ! 输出参数：海洋表面流速分量 (m/s)
   REAL(KIND = kind_cur), DIMENSION(ims:ime,jms:jme) :: ucur, vcur
   ! 局部变量：全域海洋流速数组
   REAL(KIND = kind_cur), DIMENSION(ids:idf,jds:jdf) :: ucur_g, vcur_g
   ! 流速质量控制参数
   REAL, PARAMETER :: cur_ll = 0._kind_cur, &      ! 流速下限 (m/s)
                      cur_ul = 5._kind_cur, &      ! 流速上限 (m/s)
                      cur_k = 0._kind_cur          ! 默认流速值 (m/s)

   INTEGER :: i, j               ! 循环计数器
   LOGICAL :: getcur             ! 是否需要获取流场数据的逻辑标志

   ! 检查海洋到大气耦合标志：如果io2a < 2，不需要流场数据
   IF ( io2a .LT. 2 ) RETURN
   ! 检查物理时间步和网格ID：只在物理时间步且前两个网格处理
   IF (.NOT. phys .OR. gid > 2) RETURN
   ! 检查海洋规格：如果不是规格1，发出警告
   IF (ocean_spec /= 1) CALL atm_announce('Warn: ocean currents needed',3)
   ! 输出调试信息：进入atm_getcur子程序
   CALL atm_announce('atm_getcur entered (phys = .true.)',3)

   ! 判断是否需要获取流场数据(在耦合时间步开始时)
   getcur = ((n_ts(gid)-1)/i_dtc2dta)*i_dtc2dta == n_ts(gid)-1

   ! 检查getcur标志是否与zerosf一致
   IF (getcur .NEQV. zerosf) THEN
      CALL glob_abort(1, 'Warn: getcur does not match zerosf', rc)
   END IF

   ! 如果需要获取流场数据
   IF (getcur) THEN
      ! 输出调试信息：准备接收流场数据
      CALL atm_announce('atm_getcur: to receive CUR',3)
      ! 接收X方向流速全域数据
      CALL cmp_gnr_recv(ucur_g, ngp, mpi_kind_cur)
      ! 接收Y方向流速全域数据
      CALL cmp_gnr_recv(vcur_g, ngp, mpi_kind_cur)
      ! 将全域数据分解到局部数组(X方向)
      CALL disassemble(ucur_g, ucur_cc(gid)%a, kind_cur)
      ! 将全域数据分解到局部数组(Y方向)
      CALL disassemble(vcur_g, vcur_cc(gid)%a, kind_cur)
      ! 输出调试信息：流场数据接收完成
      CALL atm_announce('atm_getcur: CUR received',3)
   END IF

   ! 如果是独立模式(无耦合器)，直接返回
   IF ( coupler_id .LT. 0 ) RETURN


   ! 对流场数据进行质量控制和赋值
   DO j = jts,jte                 ! 循环遍历J方向瓦片范围
   DO i = its,ite                 ! 循环遍历I方向瓦片范围
     ! 检查X方向流速是否在合理范围内(0-5 m/s)
     IF ( ABS(ucur_cc(gid)%a(i,j)) .GE. cur_ll .AND.                &
          ABS(ucur_cc(gid)%a(i,j)) .LE. cur_ul ) THEN
        ! 如果在合理范围内，使用接收到的流速值
        ucur(i,j) = ucur_cc(gid)%a(i,j)
     ELSE
        ! 如果超出合理范围，使用默认值(0 m/s)
        ucur(i,j) = cur_k
     ENDIF
     ! 检查Y方向流速是否在合理范围内(0-5 m/s)
     IF ( ABS(vcur_cc(gid)%a(i,j)) .GE. cur_ll .AND.                &
          ABS(vcur_cc(gid)%a(i,j)) .LE. cur_ul ) THEN
        ! 如果在合理范围内，使用接收到的流速值
        vcur(i,j) = vcur_cc(gid)%a(i,j)
     ELSE
        ! 如果超出合理范围，使用默认值(0 m/s)
        vcur(i,j) = cur_k
     ENDIF
   ENDDO
   ENDDO

END SUBROUTINE atm_getcur

! ATM_GETWSTATE - 获取波浪状态参数子程序
! 功能：从耦合器接收波浪状态参数(Charnok系数和风浪夹角)
! 作者：Biju Thomas, GSO/URI, 2015年4月8日
! 输出参数：
!   alpha - Charnok系数，用于计算海表粗糙度
!   gamma - 风浪夹角 (弧度)，风向与波浪传播方向的夹角
SUBROUTINE atm_getwstate(alpha,gamma)

! 获取波浪状态参数(Charnok系数和风浪夹角)
! Biju Thomas,  GSO/URI  on 4/8/2015
!
   USE atm_cc                     ! 使用大气耦合通信模块
   IMPLICIT NONE

   ! 输出参数：波浪状态参数
   REAL(KIND = kind_wstate), DIMENSION(ims:ime,jms:jme) :: alpha, gamma
   ! 局部变量：全域波浪状态数组
   REAL(KIND = kind_wstate), DIMENSION(ids:idf,jds:jdf) :: alpha_g, &
                                                           gamma_g
   ! 角度转换常数：度转弧度
   REAL, PARAMETER :: deg2rad=3.1415926_kind_wstate/180_kind_wstate
   ! 波浪状态参数的质量控制阈值
   REAL, PARAMETER :: alpha_ll = 0.0_kind_wstate, &      ! Charnok系数下限
                      alpha_ul = 0.2_kind_wstate, &      ! Charnok系数上限
                      alpha_k = 0.0185_kind_wstate, &    ! 默认Charnok系数
                      gamma_ll = -20.0_kind_wstate*deg2rad, & ! 风浪夹角下限(-20度)
                      gamma_ul = 20.0_kind_wstate*deg2rad, &  ! 风浪夹角上限(+20度)
                      gamma_k = 0.0_kind_wstate           ! 默认风浪夹角(0度)


   INTEGER :: i, j               ! 循环计数器
   LOGICAL :: getwstate          ! 是否需要获取波浪状态数据的逻辑标志

   ! 检查波浪到大气耦合标志：如果iw2a < 1，不需要波浪状态数据
   IF ( iw2a .LT. 1 ) RETURN
   ! 检查波浪模型ID：如果波浪模型未激活，直接返回
   IF (wm_id <= 0)  RETURN
   ! 检查物理时间步和网格ID：只在物理时间步且前两个网格处理
   IF (.NOT. phys .OR. gid > 2) RETURN
   ! 输出调试信息：进入atm_getwstate子程序
   CALL atm_announce('atm_getstate entered (phys = .true.)',3)

   ! 判断是否需要获取波浪状态数据(在耦合时间步开始时)
   getwstate = ((n_ts(gid)-1)/i_dtc2dta)*i_dtc2dta == n_ts(gid)-1

   ! 检查getwstate标志是否与zerosf一致
   IF (getwstate .NEQV. zerosf) THEN
      CALL glob_abort(1, 'Warn: getwstate does not match zerosf', rc)
   END IF

   ! 如果需要获取波浪状态数据
   IF (getwstate) THEN
      ! 输出调试信息：准备接收波浪状态数据
      CALL atm_announce('atm_getwsate: to receive WSTATE',3)
      ! 接收Charnok系数全域数据
      CALL cmp_gnr_recv(alpha_g, ngp, mpi_kind_wstate)
      ! 接收风浪夹角全域数据
      CALL cmp_gnr_recv(gamma_g, ngp, mpi_kind_wstate)
      ! 将Charnok系数全域数据分解到局部数组
      CALL disassemble(alpha_g, alpha_cc(gid)%a, kind_wstate)
      ! 将风浪夹角全域数据分解到局部数组
      CALL disassemble(gamma_g, gamma_cc(gid)%a, kind_wstate)
      ! 输出调试信息：波浪状态数据接收完成
      CALL atm_announce('atm_getwstate: WSTATE received',3)
   END IF

   ! 如果是独立模式(无耦合器)，直接返回
   IF ( coupler_id .LT. 0 ) RETURN
   

   ! 对波浪状态数据进行质量控制和赋值
   DO j = jts,jte                 ! 循环遍历J方向瓦片范围
   DO i = its,ite                 ! 循环遍历I方向瓦片范围
!     ! Charnok系数质量控制(已注释掉，直接使用接收值)
!     IF ( alpha_cc(gid)%a(i,j) .GT. alpha_ll .AND.                &
!          alpha_cc(gid)%a(i,j) .LT. alpha_ul ) THEN
!        alpha(i,j) = alpha_cc(gid)%a(i,j)
!     ELSE
!        alpha(i,j) = alpha_k
!     ENDIF
     ! 直接使用接收到的Charnok系数值(无质量控制)
     alpha(i,j) = alpha_cc(gid)%a(i,j)
     ! 检查风浪夹角是否在合理范围内(-20度到+20度)
     IF ( gamma_cc(gid)%a(i,j) .GT. gamma_ll .AND.                &
          gamma_cc(gid)%a(i,j) .LT. gamma_ul ) THEN
        ! 如果在合理范围内，使用接收到的风浪夹角值
        gamma(i,j) =  gamma_cc(gid)%a(i,j)
     ELSE
        ! 如果超出合理范围，使用默认值(0度)
        gamma(i,j) = gamma_k
     ENDIF
   ENDDO
   ENDDO

END SUBROUTINE atm_getwstate

! ATM_PREPWINDP - 准备风场数据子程序
! 功能：准备发送给波浪模型的风场和相关调整变量
! 作者：Biju Thomas, GSO/URI, 2015年4月8日
! 输入参数：
!   ulowl - 最低模式层X方向风速 (m/s)
!   vlowl - 最低模式层Y方向风速 (m/s)
!   richn - Richardson数，用于稳定度修正
!   zlowl - 最低模式层高度 (m)
SUBROUTINE atm_prepwindp(ulowl, vlowl, richn, zlowl)

! 准备风场和调整变量(最低模式层高度和Richardson数)
! Biju Thomas,  GSO/URI  on 4/8/2015
!
   USE atm_cc                     ! 使用大气耦合通信模块
   IMPLICIT NONE

   ! 输入参数：风场和调整变量
   REAL(KIND = kind_windp), DIMENSION(ims:ime,jms:jme) :: ulowl, vlowl, &
                                                          richn, zlowl
   ! 检查大气到波浪耦合标志：如果ia2w < 1，不需要发送风场数据
   IF ( ia2w .LT. 1 ) RETURN
   ! 检查波浪模型ID：如果波浪模型未激活，直接返回
   IF (wm_id <= 0)  RETURN
   ! 检查物理时间步和网格ID：只在物理时间步且前两个网格处理
   IF (.NOT. phys .OR. gid > 2) RETURN

   ! 输出调试信息：进入atm_prepwindp子程序
   CALL atm_announce('atm_atm_prepwindp: entered',3)

   ! 如果是耦合时间步的开始，将风场数据数组清零
   IF (zerosf) wwinp(gid)%a = 0.0

   ! 累积X方向风速到发送数组的第NSF_WM-1个分量
   wwinp(gid)%a(its:ite,jts:jte,NSF_WM-1) =    &  ! D.S.
                              wwinp(gid)%a(its:ite,jts:jte,NSF_WM-1) + &
                              ulowl(its:ite,jts:jte)
   ! 累积Y方向风速到发送数组的第NSF_WM个分量
   wwinp(gid)%a(its:ite,jts:jte,NSF_WM) =      &  ! D.S.
                              wwinp(gid)%a(its:ite,jts:jte,NSF_WM)   + &
                              vlowl(its:ite,jts:jte)
   ! 累积Richardson数到发送数组的第NSF_WM-3个分量
   wwinp(gid)%a(its:ite,jts:jte,NSF_WM-3) =    &  ! D.S.
                              wwinp(gid)%a(its:ite,jts:jte,NSF_WM-3) + &
                              richn(its:ite,jts:jte)
   ! 累积最低层高度到发送数组的第NSF_WM-2个分量
   wwinp(gid)%a(its:ite,jts:jte,NSF_WM-2) =    &  ! D.S.
                              wwinp(gid)%a(its:ite,jts:jte,NSF_WM-2) + &
                              zlowl(its:ite,jts:jte)
   ! 如果需要标准化风场数据(在耦合时间步结束时)
   IF (nrmsf) THEN
      ! 将累积的风场数据乘以时间比例因子，得到耦合时间步的平均值
      wwinp(gid)%a = wwinp(gid)%a*dta2dtc
   END IF

   ! 输出调试信息：从atm_prepwindp子程序返回
   CALL atm_announce('atm_atm_prepwindp: returned',3)

END SUBROUTINE atm_prepwindp


! ATM_SENDWINDP - 发送风场数据到波浪模型子程序
! 功能：将累积的风场和调整变量数据组装并发送给波浪模型
! 作者：Biju Thomas, GSO/URI, 2015年4月8日
! 发送的数据包括：U1, V1, Richardson数, 最低层高度
SUBROUTINE atm_sendwindp

! 发送风场和调整变量(U1, V1, Charnok系数和风浪夹角)
! Biju Thomas,  GSO/URI  on 4/8/2015
!
   USE atm_cc                     ! 使用大气耦合通信模块
   IMPLICIT NONE

   INTEGER :: n                   ! 风场变量循环计数器
   REAL(KIND = kind_windp), DIMENSION(ids:idf,jds:jdf) :: field  ! 组装后的全域风场数组

   ! 检查大气到波浪耦合标志：如果ia2w < 1，不需要发送风场数据
   IF ( ia2w .LT. 1 ) RETURN
   ! 检查波浪模型ID：如果波浪模型未激活，直接返回
   IF (wm_id <= 0)  RETURN
   ! 检查物理时间步和网格ID：只在物理时间步且前两个网格处理
   IF (.NOT. phys .OR. gid > 2) RETURN

   ! 检查是否需要发送风场数据：如果不是发送时间步，则返回
   IF (.NOT. sendsf) THEN
      CALL atm_announce('atm_sendwindp entered with PHYS but not sendSF: returning'// &
      sgid,3)
      RETURN
   END IF

   ! 输出调试信息：进入atm_sendwindp子程序
   CALL atm_announce('atm_prepwindp: entered'//sgid,3)

   ! 循环发送所有风场变量
   DO n = 1, NSF_WM
      ! 将分布式数组组装成全域数组
      CALL assemble(field, wwinp(gid)%a(:,:,n), kind_windp)
      ! 通过耦合器发送风场数据
      CALL cmp_gnr_send(field, ngp, mpi_kind_windp)
   END DO

   ! 输出调试信息：从atm_sendwindp子程序返回
   CALL atm_announce('atm_prepwindp: reterned'//sgid,3)

END SUBROUTINE atm_sendwindp
