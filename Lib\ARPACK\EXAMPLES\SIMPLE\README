1. Purpose 
   -------
   This directory contains simple example drivers that call ARPACK subroutine
   __aupd.f and __eupd.f to solve various eigenvalue problems using regular
   mode. These drivers illustrate how to use ARPACK in considerable detail.
   If you have never used ARPACK before, this is the place to start.

2. Naming Convention
   -----------------
   The name for each driver has the form 'XYsimp.f', where
   X - is 's' (single precision)
       or 'd' (double precision)
       or 'c' (single complex)
       or 'z' (double complex)

   Y - is 's' (symmetric)
       or 'n' (nonsymmetric) 

   Note: there is no simple driver specifically for hermitian complex.

3. Usage
   -----
   To run these drivers, you may use the makefile in this
   directory and issue, for example, "make sssimp".  Then
   execute using "sssimp".    
