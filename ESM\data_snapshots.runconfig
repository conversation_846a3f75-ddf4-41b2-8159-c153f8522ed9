#
# svn $Id: data_snapshots.runconfig 1054 2021-03-06 19:47:12Z arango $
#::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
# Copyright (c) 2002-2021 The ROMS/TOMS Group                           :::
#   Licensed under a MIT/X style license                                :::
#   See License_ROMS.txt                                                :::
#::::::::::::::::::::::::::::::::::::::::::::::::::::: Hernan G. Arango :::
#                                                                       :::
# DATA-ROMS ESMF coupling: DATA Snapshots Exporting                     :::
#                                                                       :::
# This ESMF configuration file includes the Run Sequence to couple the  :::
# DATA and ROMS components. All the components interact with the same   :::
# coupling time step.                                                   :::
#                                                                       :::
# The coupling between DATA and ROMS components is used to test the     :::
# ROMS ESMF coupling framework. The atmospheric forcing fields are      :::
# exported to ROMS from the DATA component instead of processing the    :::
# directly the input NetCDF files specified in "roms.in".  The User     :::
# needs to activate TIME_INTERP in "build_roms" to export the field     :::
# time snapshots for ROMS to carry its time interpolation.              :::
#                                                                       :::
# The User needs to check that the single coupling interval specified   :::
# here is the same as for the ROMS coupling standard input script       :::
# (keyword TimeStep).                                                   :::
#                                                                       :::
# It is not necessary to specify options to the connectors here for     :::
# the "remapMethod" since it is specified in the input coupling         :::
# metadata file (keyword CPLname) for each exchanged field.             :::
#                                                                       :::
# If the timeStep value (in seconds) coupling interval is a wildcard    :::
# (*) character used for a single outer time loop in the run sequence,  :::
# then the associated run clock is identical to the driver clock, which :::
# is set up in standard input configuration script "coupling_esmf.in":  :::
#                                                                       :::
#     @*                                                                :::
#       ...                                                             :::
#       ...                                                             :::
#     @                                                                 :::
#                                                                       :::
# Check NUOPC reference manual for more information (Chapter 3, API).   :::
#                                                                       :::
# The component label is specific to how is known in the ESMF driver.   :::
# It is set in subroutine allocate_esmf_esm, variable MODELS(:)%name.   :::
#                                                                       :::
#::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

# DOPPIO Application

runSeq::
  @10800                     # timeStep = 3 hours interval, single time loop
    DATA -> ROMS             # DATA to ROMS connector
    ROMS
    DATA
  @
::
