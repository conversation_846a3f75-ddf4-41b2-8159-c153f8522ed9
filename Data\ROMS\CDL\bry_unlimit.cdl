netcdf bry_unlimit {

dimensions:
	xi_rho = 66 ;
	eta_rho = 194 ;
	xi_u = 65 ;
	eta_u = 194 ;
	xi_v = 66 ;
	eta_v = 193 ;
	s_rho = 30 ;
        s_w = 31 ;
	bry_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
	double bry_time(bry_time) ;
		bry_time:long_name = "open boundary conditions time" ;
		bry_time:units = "seconds since 1968-05-23 00:00:00 GMT" ;
		bry_time:calendar = "gregorian" ;
	float zeta_west(bry_time, eta_rho) ;
		zeta_west:long_name = "free-surface western boundary condition" ;
		zeta_west:units = "meter" ;
		zeta_west:time = "bry_time" ;
	float zeta_east(bry_time, eta_rho) ;
		zeta_east:long_name = "free-surface eastern boundary condition" ;
		zeta_east:units = "meter" ;
		zeta_east:time = "bry_time" ;
	float zeta_south(bry_time, xi_rho) ;
		zeta_south:long_name = "free-surface southern boundary condition" ;
		zeta_south:units = "meter" ;
		zeta_south:time = "bry_time" ;
	float zeta_north(bry_time, xi_rho) ;
		zeta_north:long_name = "free-surface northern boundary condition" ;
		zeta_north:units = "meter" ;
		zeta_north:time = "bry_time" ;
	float ubar_west(bry_time, eta_u) ;
		ubar_west:long_name = "2D u-momentum western boundary condition" ;
		ubar_west:units = "meter second-1" ;
		ubar_west:time = "bry_time" ;
	float ubar_east(bry_time, eta_u) ;
		ubar_east:long_name = "2D u-momentum eastern boundary condition" ;
		ubar_east:units = "meter second-1" ;
		ubar_east:time = "bry_time" ;
	float ubar_south(bry_time, xi_u) ;
		ubar_south:long_name = "2D u-momentum southern boundary condition" ;
		ubar_south:units = "meter second-1" ;
		ubar_south:time = "bry_time" ;
	float ubar_north(bry_time, xi_u) ;
		ubar_north:long_name = "2D u-momentum northern boundary condition" ;
		ubar_north:units = "meter second-1" ;
		ubar_north:time = "bry_time" ;
	float vbar_west(bry_time, eta_v) ;
		vbar_west:long_name = "2D v-momentum western boundary condition" ;
		vbar_west:units = "meter second-1" ;
		vbar_west:time = "bry_time" ;
	float vbar_east(bry_time, eta_v) ;
		vbar_east:long_name = "2D v-momentum eastern boundary condition" ;
		vbar_east:units = "meter second-1" ;
		vbar_east:time = "bry_time" ;
	float vbar_south(bry_time, xi_v) ;
		vbar_south:long_name = "2D v-momentum southern boundary condition" ;
		vbar_south:units = "meter second-1" ;
		vbar_south:time = "bry_time" ;
	float vbar_north(bry_time, xi_v) ;
		vbar_north:long_name = "2D v-momentum northern boundary condition" ;
		vbar_north:units = "meter second-1" ;
		vbar_north:time = "bry_time" ;
	float u_west(bry_time, s_rho, eta_u) ;
		u_west:long_name = "3D u-momentum western boundary condition" ;
		u_west:units = "meter second-1" ;
		u_west:time = "bry_time" ;
	float u_east(bry_time, s_rho, eta_u) ;
		u_east:long_name = "3D u-momentum eastern boundary condition" ;
		u_east:units = "meter second-1" ;
		u_east:time = "bry_time" ;
	float u_south(bry_time, s_rho, xi_u) ;
		u_south:long_name = "3D u-momentum southern boundary condition" ;
		u_south:units = "meter second-1" ;
		u_south:time = "bry_time" ;
	float u_north(bry_time, s_rho, xi_u) ;
		u_north:long_name = "3D u-momentum northern boundary condition" ;
		u_north:units = "meter second-1" ;
		u_north:time = "bry_time" ;
	float v_west(bry_time, s_rho, eta_v) ;
		v_west:long_name = "3D v-momentum western boundary condition" ;
		v_west:units = "meter second-1" ;
		v_west:time = "bry_time" ;
	float v_east(bry_time, s_rho, eta_v) ;
		v_east:long_name = "3D v-momentum eastern boundary condition" ;
		v_east:units = "meter second-1" ;
		v_east:time = "bry_time" ;
	float v_south(bry_time, s_rho, xi_v) ;
		v_south:long_name = "3D v-momentum southern boundary condition" ;
		v_south:units = "meter second-1" ;
		v_south:time = "bry_time" ;
	float v_north(bry_time, s_rho, xi_v) ;
		v_north:long_name = "3D v-momentum northern boundary condition" ;
		v_north:units = "meter second-1" ;
		v_north:time = "bry_time" ;
	float temp_west(bry_time, s_rho, eta_rho) ;
		temp_west:long_name = "potential temperature western boundary condition" ;
		temp_west:units = "Celsius" ;
		temp_west:time = "bry_time" ;
	float temp_east(bry_time, s_rho, eta_rho) ;
		temp_east:long_name = "potential temperature eastern boundary condition" ;
		temp_east:units = "Celsius" ;
		temp_east:time = "bry_time" ;
	float temp_south(bry_time, s_rho, xi_rho) ;
		temp_south:long_name = "potential temperature southern boundary condition" ;
		temp_south:units = "Celsius" ;
		temp_south:time = "bry_time" ;
	float temp_north(bry_time, s_rho, xi_rho) ;
		temp_north:long_name = "potential temperature northern boundary condition" ;
		temp_north:units = "Celsius" ;
		temp_north:time = "bry_time" ;
	float salt_west(bry_time, s_rho, eta_rho) ;
		salt_west:long_name = "salinity western boundary condition" ;
		salt_west:time = "bry_time" ;
	float salt_east(bry_time, s_rho, eta_rho) ;
		salt_east:long_name = "salinity eastern boundary condition" ;
		salt_east:time = "bry_time" ;
	float salt_south(bry_time, s_rho, xi_rho) ;
		salt_south:long_name = "salinity southern boundary condition" ;
		salt_south:time = "bry_time" ;
	float salt_north(bry_time, s_rho, xi_rho) ;
		salt_north:long_name = "salinity northern boundary condition" ;
		salt_north:time = "bry_time" ;

// global attributes:
		:type = "BOUNDARY FORCING" ;
                :Conventions = "CF-1.4" ;
		:title = "Northeast North American (NENA) Shelf Model" ;
		:grd_file = "roms_nena_grid_2.nc" ;
		:history = "27-Mar-2002 13:11:22" ;
}
