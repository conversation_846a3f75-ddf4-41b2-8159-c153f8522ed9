netcdf ini_fennel {

dimensions:
	xi_rho = 43 ;
	xi_u = 42 ;
	xi_v = 43 ;
	xi_psi = 42 ;
	eta_rho = 82 ;
	eta_u = 82 ;
	eta_v = 81 ;
	eta_psi = 81 ;
	s_rho = 16 ;
	s_w = 17 ;
	tracer = 13 ;
	ocean_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
        double ocean_time(time) ;
                ocean_time:long_name = "time since initialization" ;
                ocean_time:units = "seconds since 1968-05-23 00:00:00 GMT" ;
                ocean_time:calendar = "gregorian" ;
	float zeta(ocean_time, eta_rho, xi_rho) ;
		zeta:long_name = "free-surface" ;
		zeta:units = "meter" ;
		zeta:time = "ocean_time" ;
		zeta:coordinates = "lon_rho lat_rho ocean_time" ;
	float ubar(ocean_time, eta_u, xi_u) ;
		ubar:long_name = "vertically integrated u-momentum component" ;
		ubar:units = "meter second-1" ;
		ubar:time = "ocean_time" ;
		ubar:coordinates = "lon_u lat_u ocean_time" ;
	float vbar(ocean_time, eta_v, xi_v) ;
		vbar:long_name = "vertically integrated v-momentum component" ;
		vbar:units = "meter second-1" ;
		vbar:time = "ocean_time" ;
		vbar:coordinates = "lon_v lat_v ocean_time" ;
	float u(ocean_time, s_rho, eta_u, xi_u) ;
		u:long_name = "u-momentum component" ;
		u:units = "meter second-1" ;
		u:time = "ocean_time" ;
		u:coordinates = "lon_u lat_u s_rho ocean_time" ;
	float v(ocean_time, s_rho, eta_v, xi_v) ;
		v:long_name = "v-momentum component" ;
		v:units = "meter second-1" ;
		v:time = "ocean_time" ;
		v:coordinates = "lon_v lat_v s_rho ocean_time" ;
	float temp(ocean_time, s_rho, eta_rho, xi_rho) ;
		temp:long_name = "potential temperature" ;
		temp:units = "Celsius" ;
		temp:time = "ocean_time" ;
		temp:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float salt(ocean_time, s_rho, eta_rho, xi_rho) ;
		salt:long_name = "salinity" ;
		salt:time = "ocean_time" ;
		salt:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float NO3(ocean_time, s_rho, eta_rho, xi_rho) ;
		NO3:long_name = "nitrate concentration" ;
		NO3:units = "millimole_N03 meter-3" ;
		NO3:time = "ocean_time" ;
		NO3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float NH4(ocean_time, s_rho, eta_rho, xi_rho) ;
		NH4:long_name = "ammonium concentration" ;
		NH4:units = "millimole_NH4 meter-3" ;
		NH4:time = "ocean_time" ;
		NH4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float chlorophyll(ocean_time, s_rho, eta_rho, xi_rho) ;
		chlorophyll:long_name = "chlorophyll concentration" ;
		chlorophyll:units = "milligrams_chlorophyll meter-3" ;
		chlorophyll:time = "ocean_time" ;
		chlorophyll:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float phytoplankton(ocean_time, s_rho, eta_rho, xi_rho) ;
		phytoplankton:long_name = "phytoplankton concentration" ;
		phytoplankton:units = "millimole_nitrogen meter-3" ;
		phytoplankton:time = "ocean_time" ;
		phytoplankton:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float zooplankton(ocean_time, s_rho, eta_rho, xi_rho) ;
		zooplankton:long_name = "zooplankton concentration" ;
		zooplankton:units = "millimole_nitrogen meter-3" ;
		zooplankton:time = "ocean_time" ;
		zooplankton:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float LdetritusN(ocean_time, s_rho, eta_rho, xi_rho) ;
		LdetritusN:long_name = "large fraction nitrogen detritus concentration" ;
		LdetritusN:units = "millimole_nitrogen meter-3" ;
		LdetritusN:time = "ocean_time" ;
		LdetritusN:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float SdetritusN(ocean_time, s_rho, eta_rho, xi_rho) ;
		SdetritusN:long_name = "small fraction nitrogen detritus concentration" ;
		SdetritusN:units = "millimole_nitrogen meter-3" ;
		SdetritusN:time = "ocean_time" ;
		SdetritusN:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float LdetritusC(ocean_time, s_rho, eta_rho, xi_rho) ;
		LdetritusC:long_name = "large fraction carbon detritus concentration" ;
		LdetritusC:units = "millimole_carbon meter-3" ;
		LdetritusC:time = "ocean_time" ;
		LdetritusC:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float SdetritusC(ocean_time, s_rho, eta_rho, xi_rho) ;
		SdetritusC:long_name = "small fraction carbon detritus concentration" ;
		SdetritusC:units = "millimole_carbon meter-3" ;
		SdetritusC:time = "ocean_time" ;
		SdetritusC:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float TIC(ocean_time, s_rho, eta_rho, xi_rho) ;
		TIC:long_name = "total inorganic carbon" ;
		TIC:units = "millimole_carbon meter-3" ;
		TIC:time = "ocean_time" ;
		TIC:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float alkalinity(ocean_time, s_rho, eta_rho, xi_rho) ;
		alkalinity:long_name = "total alkalinity" ;
		alkalinity:units = "milliequivalents meter-3" ;
		alkalinity:time = "ocean_time" ;
		alkalinity:coordinates = "lon_rho lat_rho s_rho ocean_time" ;

// global attributes:
		:type = "ROMS INITIAL file" ;
		:title = "ROMS Initial fiels for Hydrodynamics and Biology" ;
		:grd_file = "roms_grd.nc" ;

}
