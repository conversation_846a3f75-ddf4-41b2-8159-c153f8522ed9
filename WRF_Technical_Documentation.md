# WRF (Weather Research and Forecasting) 模型技术文档

## 1. 项目概述

### 1.1 基本信息
- **项目名称**: WRF (Weather Research and Forecasting) Model
- **版本**: 4.2.2
- **开发机构**: NCAR (National Center for Atmospheric Research)
- **许可证**: 公共领域软件
- **编程语言**: Fortran 90/95, C
- **支持平台**: Linux, Unix, Windows (Cygwin)

### 1.2 项目描述
WRF是一个先进的中尺度数值天气预报系统，设计用于大气研究和业务预报应用。它是一个完全可压缩、非静力学模型，具有静力学选项，支持多种物理参数化方案和数值方法。

## 2. 系统架构

### 2.1 总体架构
WRF采用分层软件架构设计：

```
┌─────────────────────────────────────────┐
│              应用层 (Application)        │
├─────────────────────────────────────────┤
│              中介层 (Mediation)          │
├─────────────────────────────────────────┤
│              模型层 (Model)              │
├─────────────────────────────────────────┤
│              驱动层 (Driver)             │
└─────────────────────────────────────────┘
```

### 2.2 核心模块组织

#### 2.2.1 主要目录结构
- **`frame/`**: 框架核心，包含域管理、I/O、通信等
- **`dyn_em/`**: EM (Eulerian Mass) 动力学核心
- **`dyn_nmm/`**: NMM (Nonhydrostatic Mesoscale Model) 动力学核心
- **`phys/`**: 物理参数化模块
- **`chem/`**: 化学模块 (WRF-Chem)
- **`share/`**: 共享工具和实用程序
- **`external/`**: 外部库接口
- **`main/`**: 主程序入口
- **`tools/`**: 开发和测试工具

#### 2.2.2 核心框架模块
```fortran
! 主要框架模块
module_driver_constants.o    ! 驱动常数
module_domain_type.o         ! 域类型定义
module_domain.o              ! 域管理
module_integrate.o           ! 时间积分
module_timing.o              ! 时间管理
module_configure.o           ! 配置管理
module_io.o                  ! I/O系统
module_comm_dm.o             ! 分布式内存通信
module_nesting.o             ! 嵌套网格
```

## 3. 动力学核心

### 3.1 EM核心 (Eulerian Mass Core)
EM核心是WRF的主要动力学核心，采用欧拉质量坐标系统。

#### 3.1.1 数值方法特征
- **网格系统**: Arakawa C网格交错
- **垂直坐标**: 地形跟随混合σ-压力坐标
- **时间积分**: 2阶和3阶Runge-Kutta方案
- **空间离散**: 2-6阶平流选项
- **声波处理**: 时间分裂小步长方案

#### 3.1.2 核心求解器
```fortran
! solve_em.F - 主要时间步进驱动程序
SUBROUTINE solve_em
  ! (1) Runge-Kutta循环
  ! (2) 非时间分裂物理过程
  ! (3) 小时间步长循环(声波)
  ! (4) 标量平流
```

#### 3.1.3 主要模块
- **`module_small_step_em.F`**: 小时间步长积分
- **`module_big_step_utilities_em.F`**: 大时间步长工具
- **`module_advect_em.F`**: 平流方案
- **`module_diffusion_em.F`**: 扩散过程
- **`module_bc_em.F`**: 边界条件

### 3.2 NMM核心 (Nonhydrostatic Mesoscale Model)
NMM核心采用不同的数值方法和网格系统。

#### 3.2.1 数值方法特征
- **网格系统**: Arakawa E网格
- **时间步长**: 所有项使用相同时间步长
- **水平快波**: 前向-后向格式
- **垂直声波**: 隐式格式
- **平流方案**: Adams-Bashforth和Crank-Nicholson

## 4. 物理参数化

### 4.1 物理模块架构
WRF包含多种物理参数化方案，通过统一的驱动程序管理：

#### 4.1.1 微物理方案
```fortran
! 支持的微物理方案
CASE (KESSLERSCHEME)     ! Kessler暖雨方案
CASE (WSM3SCHEME)        ! WSM3类方案
CASE (WSM5SCHEME)        ! WSM5方案
CASE (WSM6SCHEME)        ! WSM6方案
CASE (THOMPSONSCHEME)    ! Thompson方案
CASE (MORRISONSCHEME)    ! Morrison双矩方案
CASE (P3_1CATEGORY)      ! P3单类别方案
```

#### 4.1.2 积云对流方案
- **Kain-Fritsch方案**: 包含浅对流
- **Betts-Miller-Janjic方案**
- **Grell-Devenyi集合方案**
- **Grell 3D方案**
- **Tiedtke方案**: 包含动量输送
- **Zhang-McFarlane方案**

#### 4.1.3 边界层方案
```fortran
! 边界层参数化选项
pbl_physics = 1  ! YSU方案
pbl_physics = 2  ! Mellor-Yamada-Janjic
pbl_physics = 4  ! QNSE-EDMF
pbl_physics = 5  ! MYNN 2.5阶
pbl_physics = 6  ! MYNN 3阶
pbl_physics = 7  ! ACM2
pbl_physics = 8  ! BouLac
```

#### 4.1.4 辐射方案
- **长波辐射**: RRTM, CAM, RRTMG, Goddard, FLG
- **短波辐射**: Dudhia, Goddard, CAM, RRTMG, FLG
- **地形坡度和阴影效应**

#### 4.1.5 陆面过程
- **Noah陆面模式** (4层)
- **Noah-MP** (4层)
- **RUC陆面模式** (6层)
- **CLM4** (10层)
- **SSiB** (3层)

### 4.2 物理过程驱动
```fortran
! 物理过程初始化
SUBROUTINE phy_init
  ! 微物理初始化
  CALL microphysics_init
  ! 积云对流初始化  
  CALL cumulus_init
  ! 边界层初始化
  CALL bl_init
  ! 辐射初始化
  CALL radiation_init
```

## 5. 数值方法

### 5.1 时间积分方案
WRF采用时间分裂方法处理不同时间尺度的过程：

#### 5.1.1 Runge-Kutta方案
- **2阶RK方案**: 计算效率高
- **3阶RK方案**: 推荐使用，精度更高
- **时间步长**: 大步长用于平流和物理过程，小步长用于声波

#### 5.1.2 小时间步长处理
```fortran
! 声波时间步长循环
small_steps: DO step = 1, acoustic_steps
  ! 水平动量推进
  CALL advance_uv
  ! μ和θ推进  
  CALL advance_mu_t
  ! 垂直速度和位势推进
  CALL advance_w
END DO small_steps
```

### 5.2 空间离散化
- **水平离散**: 有限差分，2-6阶精度选项
- **垂直离散**: 地形跟随坐标
- **平流方案**: 
  - 正定平流 (单调性保持)
  - WENO方案 (高阶精度)
  - 通量校正输送

### 5.3 边界条件
- **开放边界**: 辐射边界条件
- **周期边界**: 全球模拟
- **对称边界**: 理想化试验
- **嵌套边界**: 多重网格

## 6. 并行计算架构

### 6.1 并行化策略
WRF支持多层次并行计算：

#### 6.1.1 分布式内存并行 (MPI)
```fortran
! MPI通信模块
module_comm_dm.o           ! 分布式内存通信
module_comm_nesting_dm.o   ! 嵌套网格通信
```

#### 6.1.2 共享内存并行 (OpenMP)
- 支持混合MPI+OpenMP模式
- 线程级并行化物理过程

#### 6.1.3 域分解
- **水平分解**: 2D块分解
- **负载均衡**: 自适应分解算法
- **通信优化**: Halo交换和重叠通信

### 6.2 I/O并行化
```fortran
! I/O quilting系统
module_io_quilt.o          ! 异步I/O
! 支持的I/O格式
#ifdef NETCDF
#ifdef PHDF5              ! 并行HDF5
#ifdef PNETCDF            ! 并行NetCDF
```

## 7. I/O系统

### 7.1 支持的数据格式
- **NetCDF**: 主要输出格式
- **HDF5**: 并行I/O支持
- **GRIB1/GRIB2**: 气象标准格式
- **Binary**: 内部格式

### 7.2 I/O架构
```fortran
! I/O系统初始化
SUBROUTINE wrf_ioinit
#ifdef NETCDF
  CALL ext_ncd_ioinit
#endif
#ifdef PHDF5  
  CALL ext_phdf5_ioinit
#endif
```

### 7.3 数据流管理
- **输入流**: 初始条件、边界条件
- **输出流**: 历史文件、重启文件、诊断文件
- **辅助流**: 观测数据、分析场

## 8. 配置和编译系统

### 8.1 配置选项
```bash
# 配置脚本
./configure [选项]
  -d    调试模式
  -D    调试+浮点陷阱
  -r8   8字节实数
  chem  化学模块
  arw   ARW核心
  nmm   NMM核心
```

### 8.2 编译目标
```bash
# 编译选项
compile em_real          # 真实数据案例
compile em_ideal         # 理想化案例
compile em_quarter_ss    # 超级单体
compile em_les           # 大涡模拟
```

### 8.3 依赖库
- **NetCDF**: 必需
- **MPI**: 并行计算
- **HDF5**: 可选I/O格式
- **ESMF**: 耦合框架支持

## 9. 测试案例

### 9.1 理想化案例
- **`em_b_wave`**: Baroclinic波
- **`em_quarter_ss`**: 超级单体雷暴
- **`em_les`**: 大涡模拟
- **`em_hill2d_x`**: 山地波动
- **`em_squall2d_x/y`**: 飑线

### 9.2 真实数据案例
- **`em_real`**: 真实天气预报
- **`em_tropical_cyclone`**: 热带气旋
- **`nmm_real`**: NMM真实案例

## 10. 扩展功能

### 10.1 WRF-Chem
化学传输模块，支持：
- 气体相化学
- 气溶胶过程
- 光解反应
- 干湿沉降

### 10.2 WRF-Hydro
水文模块，包括：
- 陆面水文过程
- 河道汇流
- 地下水模拟

### 10.3 数据同化
- **WRF-Var**: 变分数据同化
- **WRFDA**: 高级数据同化系统
- **观测算子**: 雷达、卫星数据

## 11. 性能优化

### 11.1 计算优化
- **向量化**: 支持现代CPU向量指令
- **GPU加速**: CUDA支持(部分模块)
- **编译器优化**: 支持多种编译器

### 11.2 内存管理
- **动态内存分配**
- **内存池管理**
- **缓存优化**

### 11.3 通信优化
- **非阻塞通信**
- **通信聚合**
- **拓扑感知分解**

## 12. 嵌套网格系统

### 12.1 嵌套类型
WRF支持多种嵌套配置：

#### 12.1.1 双向嵌套
- **反馈机制**: 细网格结果反馈到粗网格
- **网格比例**: 支持奇数和偶数比例
- **平滑选项**: 减少反馈噪声

#### 12.1.2 单向嵌套
- **强制边界**: 仅从粗网格获取边界条件
- **计算效率**: 降低计算复杂度

#### 12.1.3 移动嵌套
```fortran
! 移动嵌套配置
move_nest = .true.
move_id = 2                    ! 移动的域ID
move_interval = 60             ! 移动间隔(分钟)
move_cd_x = 1                  ! X方向移动格点数
move_cd_y = 1                  ! Y方向移动格点数
```

### 12.2 嵌套通信
```fortran
! 嵌套网格通信模块
module_nesting.o               ! 嵌套管理
module_comm_nesting_dm.o       ! 嵌套通信
```

## 13. 注册表系统 (Registry)

### 13.1 注册表架构
WRF使用注册表系统管理模型状态变量：

#### 13.1.1 主要注册表文件
- **`Registry.EM`**: EM核心变量定义
- **`Registry.NMM`**: NMM核心变量定义
- **`registry.chem`**: 化学变量
- **`registry.fire`**: 火灾模块变量

#### 13.1.2 变量类型
```fortran
! 状态变量类型
state real u ikjftb dyn_em 1 X \
  "U" "x-wind component" "m s-1"

state real v ikjftb dyn_em 1 Y \
  "V" "y-wind component" "m s-1"

state real w ikjftb dyn_em 1 Z \
  "W" "z-wind component" "m s-1"
```

### 13.2 代码生成
注册表系统自动生成：
- **内存分配代码**
- **I/O接口代码**
- **通信代码**
- **嵌套插值代码**

## 14. 诊断和后处理

### 14.1 内置诊断
WRF提供多种内置诊断变量：

#### 14.1.2 气象诊断
```fortran
! 诊断模块
module_diag_functions.F        ! 基本诊断函数
module_diag_misc.F            ! 杂项诊断
module_diag_afwa.F            ! AFWA诊断
module_diag_nwp.F             ! 数值天气预报诊断
```

#### 14.1.2 专业诊断
- **雷达反射率**: 模拟雷达观测
- **卫星亮温**: 模拟卫星观测
- **飞行层诊断**: 航空气象
- **能见度**: 交通气象

### 14.2 输出控制
```fortran
! 输出流配置
&time_control
 auxhist1_outname = "wrfout_d<domain>_<date>"
 auxhist1_interval = 60         ! 输出间隔
 frames_per_auxhist1 = 1000     ! 每文件帧数
 io_form_auxhist1 = 2           ! NetCDF格式
/
```

## 15. 数据同化接口

### 15.1 观测算子
WRF提供多种观测算子支持数据同化：

#### 15.1.1 常规观测
- **地面观测**: 温度、湿度、风速、气压
- **探空观测**: 垂直廓线数据
- **飞机观测**: 商业航班数据

#### 15.1.2 遥感观测
- **雷达数据**: 径向速度、反射率
- **卫星数据**: 辐射率、反演产品
- **GPS掩星**: 折射率数据

### 15.2 同化方法
```fortran
! 数据同化选项
fdda_on = .true.               ! 启用FDDA
grid_fdda = 1                  ! 网格点nudging
obs_nudge_opt = 1              ! 观测nudging
```

## 16. 化学传输模块 (WRF-Chem)

### 16.1 化学机制
WRF-Chem支持多种化学机制：

#### 16.1.1 气相化学
- **RADM2**: 区域酸沉降模式
- **RACM**: 区域大气化学机制
- **CBM-Z**: 碳键机制
- **MOZART**: 全球化学传输

#### 16.1.2 气溶胶模块
```fortran
! 气溶胶方案
chem_opt = 112                 ! GOCART方案
chem_opt = 201                 ! MOSAIC方案
chem_opt = 301                 ! SORGAM方案
```

### 16.2 排放处理
- **人为排放**: 工业、交通、居民
- **生物源排放**: MEGAN模式
- **火灾排放**: 生物质燃烧
- **海盐排放**: 海洋源

## 17. 水文模块 (WRF-Hydro)

### 17.1 水文过程
WRF-Hydro耦合陆面和水文过程：

#### 17.1.1 陆面水文
- **入渗过程**: Richards方程
- **蒸散发**: Penman-Monteith方程
- **积雪过程**: 能量平衡模式

#### 17.1.2 河道汇流
```fortran
! 水文配置
&hydro_nlist
 channel_option = 2             ! 河道路由选项
 udmp_opt = 1                   ! 扩散波方程
 route_lake_f = .true.          ! 湖泊路由
/
```

## 18. 高级功能

### 18.1 自适应时间步长
```fortran
! 自适应时间步长配置
use_adaptive_time_step = .true.
step_to_output_time = .true.
target_cfl = 1.2               ! 目标CFL数
max_step_increase_pct = 5      ! 最大步长增加百分比
```

### 18.2 随机物理扰动
```fortran
! 随机物理参数化
stoch_force_opt = 1            ! 随机强迫
stoch_vertstruc_opt = 1        ! 垂直结构选项
tot_backscat_psi = 1.0E-05     ! 后向散射系数
```

### 18.3 混合垂直坐标
```fortran
! 混合坐标配置
hybrid_opt = 2                 ! 混合坐标选项
etac = 0.2                     ! 混合坐标参数
```

## 19. 性能调优指南

### 19.1 编译优化
```bash
# 编译器优化选项
export FCFLAGS="-O3 -xHost -ip"
export CFLAGS="-O3 -xHost -ip"

# 并行编译
export J="-j 8"
```

### 19.2 运行时优化
```bash
# MPI进程绑定
export I_MPI_PIN_DOMAIN=omp
export I_MPI_PIN_PROCESSOR_LIST=0-47

# OpenMP线程设置
export OMP_NUM_THREADS=4
export OMP_STACKSIZE=128M
```

### 19.3 内存优化
```fortran
! 内存使用优化
nproc_x = 4                    ! X方向进程数
nproc_y = 6                    ! Y方向进程数
numtiles = 1                   ! 瓦片数量
```

## 20. 故障排除

### 20.1 常见错误
- **CFL违反**: 减小时间步长或增加扩散
- **内存不足**: 增加节点数或减少域大小
- **数值不稳定**: 检查初始条件和边界条件

### 20.2 调试工具
```fortran
! 调试选项
debug_level = 100              ! 调试级别
nocolons = .true.              ! 文件名格式
```

### 20.3 性能监控
```bash
# 性能分析工具
mpirun -np 24 -profile wrf.exe
vtune -collect hotspots wrf.exe
```

## 21. 版本历史和发展

### 21.1 主要版本
- **V1.0** (2000): 初始版本
- **V2.0** (2004): 重大重构
- **V3.0** (2008): WPS集成
- **V4.0** (2018): 现代化架构
- **V4.2.2** (当前): 稳定版本

### 21.2 未来发展
- **GPU加速**: 更多模块GPU化
- **机器学习**: AI辅助参数化
- **云原生**: 容器化部署
- **实时预报**: 流式处理

---

*本文档基于WRF 4.2.2版本编写，涵盖了WRF模型的核心技术架构、数值方法、物理参数化、并行计算、I/O系统等关键技术要点。详细的使用说明和最新信息请参考WRF官方文档和用户手册。*
