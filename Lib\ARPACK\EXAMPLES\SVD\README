1. Purpose 
   -------
   This directory contains simple example drivers that call ARPACK subroutine
   [s,d]saupd.f and [s,d]seupd.f to compute the Singular Value Decomposition
   of a matrix A.  These drivers illustrate how to use ARPACK in considerable 
   detail.

2. Naming Convention
   -----------------
   The name for each driver has the form 'Xsvd.f', where
   X - is 's' (single precision)
       or 'd' (double precision)

3. Usage
   -----
   To run these drivers, you may use the makefile in this
   directory and issue, for example, "make ssvd".  Then
   execute using "ssvd".    
