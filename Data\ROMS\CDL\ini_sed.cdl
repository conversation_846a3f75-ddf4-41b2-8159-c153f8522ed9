netcdf ini_sed {

dimensions:
	xi_rho = 102 ;
	xi_u = 101 ;
	xi_v = 102 ;
	xi_psi = 101 ;
	eta_rho = 22 ;
	eta_u = 22 ;
	eta_v = 21 ;
	eta_psi = 21 ;
	s_rho = 8 ;
	s_w = 9 ;
	tracer = 3 ;
	Nbed = 1 ;
	ocean_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
	double ocean_time(time) ;
		ocean_time:long_name = "time since initialization" ;
		ocean_time:units = "seconds since 0000-01-01 00:00:00" ;
		ocean_time:calendar = "365.25 days in every year" ;
	float zeta(ocean_time, eta_rho, xi_rho) ;
		zeta:long_name = "free-surface" ;
		zeta:units = "meter" ;
		zeta:time = "ocean_time" ;
                zeta:coordinates = "lon_rho lat_rho ocean_time" ;
	float ubar(ocean_time, eta_u, xi_u) ;
		ubar:long_name = "vertically integrated u-momentum component" ;
		ubar:units = "meter second-1" ;
		ubar:time = "ocean_time" ;
                ubar:coordinates = "lon_u lat_u ocean_time" ;
	float vbar(ocean_time, eta_v, xi_v) ;
		vbar:long_name = "vertically integrated v-momentum component" ;
		vbar:units = "meter second-1" ;
		vbar:time = "ocean_time" ;
                vbar:coordinates = "lon_v lat_v ocean_time" ;
	float u(ocean_time, s_rho, eta_u, xi_u) ;
		u:long_name = "u-momentum component" ;
		u:units = "meter second-1" ;
		u:time = "ocean_time" ;
                u:coordinates = "lon_u lat_u s_rho ocean_time" ;
	float v(ocean_time, s_rho, eta_v, xi_v) ;
		v:long_name = "v-momentum component" ;
		v:units = "meter second-1" ;
		v:time = "ocean_time" ;
                v:coordinates = "lon_v lat_v s_rho ocean_time" ;
	float temp(ocean_time, s_rho, eta_rho, xi_rho) ;
		temp:long_name = "potential temperature" ;
		temp:units = "Celsius" ;
		temp:time = "ocean_time" ;
		temp:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float salt(ocean_time, s_rho, eta_rho, xi_rho) ;
		salt:long_name = "salinity" ;
		salt:units = "PSU" ;
		salt:time = "ocean_time" ;
		salt:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float mud_01(ocean_time, s_rho, eta_rho, xi_rho) ;
		mud_01:long_name = "suspended cohesive sediment, size class 01" ;
		mud_01:size_class = " 1.0000E-02 millimeter" ;
		mud_01:units = "milligram liter-1" ;
		mud_01:time = "ocean_time" ;
		mud_01:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float mudfrac_01(ocean_time, Nbed, eta_rho, xi_rho) ;
		mudfrac_01:long_name = "cohesive sediment fraction, size class 01" ;
		mudfrac_01:size_class = " 1.0000E-02 millimeter" ;
		mudfrac_01:time = "ocean_time" ;
		mudfrac_01:coordinates = "lon_rho lat_rho ocean_time" ;
	float mudmass_01(ocean_time, Nbed, eta_rho, xi_rho) ;
		mudmass_01:long_name = "cohesive sediment mass, size class 01" ;
		mudmass_01:size_class = " 1.0000E-02 millimeter" ;
		mudmass_01:units = "kilogram meter-2" ;
		mudmass_01:time = "ocean_time" ;
		mudmass_01:coordinates = "lon_rho lat_rho ocean_time" ;
	float bed_thickness(ocean_time, Nbed, eta_rho, xi_rho) ;
		bed_thickness:long_name = "sediment layer thickness" ;
		bed_thickness:units = "meter" ;
		bed_thickness:time = "ocean_time" ;
		bed_thickness:coordinates = "lon_rho lat_rho ocean_time" ;
	float bed_age(ocean_time, Nbed, eta_rho, xi_rho) ;
		bed_age:long_name = "sediment layer age" ;
		bed_age:units = "day" ;
		bed_age:time = "ocean_time" ;
		bed_age:coordinates = "lon_rho lat_rho ocean_time" ;
	float bed_porosity(ocean_time, Nbed, eta_rho, xi_rho) ;
		bed_porosity:long_name = "sediment layer porosity" ;
		bed_porosity:time = "ocean_time" ;
		bed_porosity:coordinates = "lon_rho lat_rho ocean_time" ;
	float grain_size(ocean_time, eta_rho, xi_rho) ;
		grain_size:long_name = "sediment median grain diameter size" ;
		grain_size:units = "meter" ;
		grain_size:time = "ocean_time" ;
		grain_size:coordinates = "lon_rho lat_rho ocean_time" ;
	float grain_density(ocean_time, eta_rho, xi_rho) ;
		grain_density:long_name = "sediment medina grain density" ;
		grain_density:units = "kilogram meter3" ;
		grain_density:time = "ocean_time" ;
		grain_density:coordinates = "lon_rho lat_rho ocean_time" ;
	float settling_vel(ocean_time, eta_rho, xi_rho) ;
		settling_vel:long_name = "sediment median grain settling velocity" ;
		settling_vel:units = "meter second-1" ;
		settling_vel:time = "ocean_time" ;
		settling_vel:coordinates = "lon_rho lat_rho ocean_time" ;
	float erosion_stress(ocean_time, eta_rho, xi_rho) ;
		erosion_stress:long_name = "sediment median critical erosion stress" ;
		erosion_stress:units = "meter2 second-2" ;
		erosion_stress:time = "ocean_time" ;
		erosion_stress:coordinates = "lon_rho lat_rho ocean_time" ;
	float bed_wave_amp(ocean_time, eta_rho, xi_rho) ;
		bed_wave_amp:long_name = "bed wave excursion amplitude" ;
		bed_wave_amp:units = "meter" ;
		bed_wave_amp:time = "ocean_time" ;
		bed_wave_amp:coordinates = "lon_rho lat_rho ocean_time" ;

// global attributes:
		:type = "ROMS INITIAL file" ;
		:title = "ROMS Initial fiels for Hydrodynamics and Sediment" ;
		:grd_file = "roms_grd.nc" ;

}
