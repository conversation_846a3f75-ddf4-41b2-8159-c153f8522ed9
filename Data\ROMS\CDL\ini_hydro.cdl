netcdf ini_hydro {

dimensions:
	xi_rho = 130 ;
	xi_u = 129 ;
	xi_v = 130 ;
	eta_rho = 130 ;
	eta_u = 130 ;
	eta_v = 129 ;
	s_rho = 20 ;
	s_w = 21 ;
	tracer = 2 ;
	ocean_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
	double ocean_time(ocean_time) ;
		ocean_time:long_name = "time since initialization" ;
		ocean_time:units = "seconds since 0000-01-01 00:00:00" ;
		ocean_time:calendar = "360.0 days in every year" ;
	double zeta(ocean_time, eta_rho, xi_rho) ;
		zeta:long_name = "free-surface" ;
		zeta:units = "meter" ;
		zeta:time = "ocean_time" ;
                zeta:coordinates = "lon_rho lat_rho ocean_time" ;
	double ubar(ocean_time, eta_u, xi_u) ;
		ubar:long_name = "vertically integrated u-momentum component" ;
		ubar:units = "meter second-1" ;
		ubar:time = "ocean_time" ;
                ubar:coordinates = "lon_u lat_u ocean_time" ;
	double vbar(ocean_time, eta_v, xi_v) ;
		vbar:long_name = "vertically integrated v-momentum component" ;
		vbar:units = "meter second-1" ;
		vbar:time = "ocean_time" ;
                vbar:coordinates = "lon_v lat_v ocean_time" ;
	double u(ocean_time, s_rho, eta_u, xi_u) ;
		u:long_name = "u-momentum component" ;
		u:units = "meter second-1" ;
		u:time = "ocean_time" ;
                u:coordinates = "lon_u lat_u s_rho ocean_time" ;
	double v(ocean_time, s_rho, eta_v, xi_v) ;
		v:long_name = "v-momentum component" ;
		v:units = "meter second-1" ;
		v:time = "ocean_time" ;
                v:coordinates = "lon_v lat_v s_rho ocean_time" ;
	double temp(ocean_time, s_rho, eta_rho, xi_rho) ;
		temp:long_name = "potential temperature" ;
		temp:units = "Celsius" ;
		temp:time = "ocean_time" ;
                temp:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	double salt(ocean_time, s_rho, eta_rho, xi_rho) ;
		salt:long_name = "salinity" ;
		salt:time = "ocean_time" ;
                salt:coordinates = "lon_rho lat_rho s_rho ocean_time" ;

// global attributes:
		:type = "INITIALIZATION file" ;
		:title = "Levitus One-degree Annual Climatology (1994) - DAMEE # 4" ;
		:out_file = "damee4_levfeb.nc" ;
		:grd_file = "damee4_grid_a.nc" ;
		:history = "Tuesday - March 14, 2000 - 5:04:02 PM" ;
}
