############################################################################
#
#  Program:         ARPACK
#
#  Module:          ARmake.inc
#
#  Purpose:         Top-level Definitions
#
#  Creation date:   February 22, 1996
#
#  Modified:
#
#  Send bug reports, comments or <NAME_EMAIL>
#
############################################################################
#
# %---------------------------------%
# |  SECTION 1: PATHS AND LIBRARIES |
# %---------------------------------%
#
# %---------------------------------------------------------%
# | The complete path to the top level of ARPACK directory, |
# | usually $(HOME)/ARPACK                                  |
# %---------------------------------------------------------%
#
home          = $(HOME)/ARPACK
#
#  %--------------------------------------%
#  | The platform identifier to suffix to |
#  | the end of library names             |
#  %--------------------------------------%
#
PLAT          = I860
#
#  %--------------------------------------------------------------%
#  | The communication library ( need for parallel library only ) |
#  | Current availible platforms are: BLACS or MPI                |
#  %--------------------------------------------------------------%
#
COMMLIB       = BLACS
#
#  %------------------------------------------------------%
#  | The directories to find the various pieces of ARPACK |
#  %------------------------------------------------------%
#
BLASdir      = $(home)/BLAS
LAPACKdir    = $(home)/LAPACK
UTILdir      = $(home)/UTIL
SRCdir       = $(home)/SRC
PSRCdir      = $(home)/PARPACK/SRC/$(COMMLIB)
PUTILdir     = $(home)/PARPACK/UTIL/$(COMMLIB)
#
#DIRS   = $(BLASdir) $(LAPACKdir) $(UTILdir) $(SRCdir)
#
# %-------------------------------------------------------------------%
# | Comment out the previous line and uncomment the following         |
# | if you already have the BLAS and LAPACK installed on your system. |
# | NOTE: ARPACK assumes the use of LAPACK version 2 codes.           |
# %-------------------------------------------------------------------%
#
DIRS   = $(LAPACKdir) $(UTILdir) $(SRCdir)
#

#
# %---------------------------------------------------%
# | The name of the libraries to be created/linked to |
# %---------------------------------------------------%
#

ARPACKLIB  = $(home)/libarpack_$(PLAT).a
LAPACKLIB = 
BLASLIB = -lkmath

ALIBS =  $(ARPACKLIB) $(LAPACKLIB) $(BLASLIB) 

#
# %-------------------------------------------------%
# | Libraries needed for Parallel ARPACK - NX-BLACS |
# %-------------------------------------------------%
#

PARPACKLIB = $(home)/parpack_$(COMMLIB)-$(PLAT).a

BLACSDBGLVL = 0
BLACSLIBS = $(HOME)/BLACS/LIB/blacs_$(COMMLIB)-$(PLAT)-$(BLACSDBGLVL).a

PLIBS = $(PARPACKLIB) $(ALIBS) $(BLACSLIBS)

#
# %---------------------------------------------------------%
# |                  SECTION 2: COMPILERS                   |
# |                                                         |
# | The following macros specify compilers, linker/loaders, |
# | the archiver, and their options.  You need to make sure |
# | these are correct for your system.                      |
# %---------------------------------------------------------%
#
#
# %------------------------------%
# | Make our own suffixes' list. |
# %------------------------------%
#
.SUFFIXES:
.SUFFIXES:	.f	.o

#
# %------------------%
# | Default command. |
# %------------------%
#
.DEFAULT:
	@$(ECHO) "Unknown target $@, try:  make help"

#
# %-------------------------------------------%
# |  Command to build .o files from .f files. |
# %-------------------------------------------%
#
.f.o:
	@$(ECHO) Making $@ from $<
	@$(FC) -c $(FFLAGS) $<

#
# %-----------------------------------------%
# | Various compilation programs and flags. |
# | You need to make sure these are correct |
# | for your system.                        |
# %-----------------------------------------%
#
FC		= if77
FFLAGS	= -O4

#
# %-----------------------------------%
# | Set the parallel fortran compiler |
# %-----------------------------------%
#
PFC     = if77
PFFLAGS = -O4

LDFLAGS = 
CD		= cd

ECHO	 = echo

LN	 = ln
LNFLAGS	 = -s

MAKE	 = /bin/make

RM	 = rm
RMFLAGS  = -f

SHELL	 = /bin/sh

#
#  %----------------------------------------------------------------%
#  | The archiver and the flag(s) to use when building an archive   |
#  | (library).  Also the ranlib routine.  If your system has no    |
#  | ranlib, set RANLIB = touch.                                    |
#  %----------------------------------------------------------------%
#
AR = ar860
ARFLAGS = rv

RANLIB  = touch
#RANLIB   = ranlib
#
# %----------------------------------%
# | This is the general help target. |
# %----------------------------------%
#
help:
	@$(ECHO) "usage: make ?"
