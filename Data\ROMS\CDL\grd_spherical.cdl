netcdf grd_spherical {

dimensions:
	xi_psi = 110 ;
	xi_rho = 111 ;
	xi_u = 110 ;
	xi_v = 111 ;
	eta_psi = 240 ;
	eta_rho = 241 ;
	eta_u = 241 ;
	eta_v = 240 ;
	bath = UNLIMITED ; // (0 currently)

variables:
	int spherical ;
		spherical:long_name = "grid type logical switch" ;
		spherical:flag_values = "0, 1" ;
		spherical:flag_meanings = "Cartesian spherical" ;
	double xl ;
		xl:long_name = "domain length in the XI-direction" ;
		xl:units = "meter" ;
	double el ;
		el:long_name = "domain length in the ETA-direction" ;
		el:units = "meter" ;
	double hraw(bath, eta_rho, xi_rho) ;
		hraw:long_name = "Working bathymetry at RHO-points" ;
		hraw:units = "meter" ;
                hraw:coordinates = "lon_rho lat_rho bath" ;
	double h(eta_rho, xi_rho) ;
		h:long_name = "Final bathymetry at RHO-points" ;
		h:units = "meter" ;
		h:coordinates = "lon_rho lat_rho" ;
	double f(eta_rho, xi_rho) ;
		f:long_name = "Coriolis parameter at RHO-points" ;
		f:units = "second-1" ;
		f:coordinates = "lon_rho lat_rho" ;
	double pm(eta_rho, xi_rho) ;
		pm:long_name = "curvilinear coordinate metric in XI" ;
		pm:units = "meter-1" ;
		pm:coordinates = "lon_rho lat_rho" ;
	double pn(eta_rho, xi_rho) ;
		pn:long_name = "curvilinear coordinate metric in ETA" ;
		pn:units = "meter-1" ;
		pn:coordinates = "lon_rho lat_rho" ;
	double dndx(eta_rho, xi_rho) ;
		dndx:long_name = "xi derivative of inverse metric factor pn" ;
		dndx:units = "meter" ;
		dndx:coordinates = "lon_rho lat_rho" ;
	double dmde(eta_rho, xi_rho) ;
		dmde:long_name = "eta derivative of inverse metric factor pm" ;
		dmde:units = "meter" ;
		dmde:coordinates = "lon_rho lat_rho" ;
	double x_rho(eta_rho, xi_rho) ;
		x_rho:long_name = "x location of RHO-points" ;
		x_rho:units = "meter" ;
	double y_rho(eta_rho, xi_rho) ;
		y_rho:long_name = "y location of RHO-points" ;
		y_rho:units = "meter" ;
	double x_psi(eta_psi, xi_psi) ;
		x_psi:long_name = "x location of PSI-points" ;
		x_psi:units = "meter" ;
	double y_psi(eta_psi, xi_psi) ;
		y_psi:long_name = "y location of PSI-points" ;
		y_psi:units = "meter" ;
	double x_u(eta_u, xi_u) ;
		x_u:long_name = "x location of U-points" ;
		x_u:units = "meter" ;
	double y_u(eta_u, xi_u) ;
		y_u:long_name = "y location of U-points" ;
		y_u:units = "meter" ;
	double x_v(eta_v, xi_v) ;
		x_v:long_name = "x location of V-points" ;
		x_v:units = "meter" ;
	double y_v(eta_v, xi_v) ;
		y_v:long_name = "y location of V-points" ;
		y_v:units = "meter" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
		lat_rho:standard_name = "latitude" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
		lon_rho:standard_name = "longitude" ;
	double lat_psi(eta_psi, xi_psi) ;
		lat_psi:long_name = "latitude of PSI-points" ;
		lat_psi:units = "degree_north" ;
		lat_psi:standard_name = "latitude" ;
	double lon_psi(eta_psi, xi_psi) ;
		lon_psi:long_name = "longitude of PSI-points" ;
		lon_psi:units = "degree_east" ;
		lon_psi:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
		lat_u:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
		lon_u:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
		lat_v:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
		lon_v:standard_name = "longitude" ;
        double angle(eta_rho, xi_rho) ;
                angle:long_name = "angle between XI-axis and EAST" ;
                angle:units = "radians" ;
		angle:coordinates = "lon_rho lat_rho" ;
	double mask_rho(eta_rho, xi_rho) ;
		mask_rho:long_name = "mask on RHO-points" ;
		mask_rho:flag_values = 0., 1. ;
		mask_rho:flag_meanings = "land water" ;
		mask_rho:coordinates = "lon_rho lat_rho" ;
	double mask_u(eta_u, xi_u) ;
		mask_u:long_name = "mask on U-points" ;
		mask_u:flag_values = 0., 1. ;
		mask_u:flag_meanings = "land water" ;
		mask_u:coordinates = "lon_u lat_u" ;
	double mask_v(eta_v, xi_v) ;
		mask_v:long_name = "mask on V-points" ;
		mask_v:flag_values = 0., 1. ;
		mask_v:flag_meanings = "land water" ;
		mask_v:coordinates = "lon_v lat_v" ;
	double mask_psi(eta_psi, xi_psi) ;
		mask_psi:long_name = "mask on PSI-points" ;
		mask_psi:flag_values = 0., 1. ;
		mask_psi:flag_meanings = "land water" ;
		mask_psi:coordinates = "lon_rho lat_rho" ;
	double wtype_grid(eta_rho, xi_rho) ;
		wtype_grid:long_name = "Jerlov water type index" ;
		wtype_grid:coordinates = "lon_rho lat_rho" ;
	double rdrag(eta_rho, xi_rho) ;
		rdrag:long_name = "linear bottom drag coefficient" ;
		rdrag:units = "meter second-1" ;
		rdrag:coordinates = "lon_rho lat_rho" ;
	double rdrag2(eta_rho, xi_rho) ;
		rdrag2:long_name = "quadratic bottom drag coefficient" ;
		rdrag2:coordinates = "lon_rho lat_rho" ;
	double ZoBot(eta_rho, xi_rho) ;
		ZoBot:long_name = "time invariant, bottom roughness length" ;
		ZoBot:units = "meter" ;
		ZoBot:coordinates = "lon_rho lat_rho" ;
	double diff_factor(eta_rho, xi_rho) ;
		diff_factor:long_name = "horizontal diffusivity factor" ;
		diff_factor:valid_min = 0. ;
		diff_factor:coordinates = "lon_rho lat_rho" ;
	double visc_factor(eta_rho, xi_rho) ;
		visc_factor:long_name = "horizontal viscosity factor" ;
		visc_factor:valid_min = 0. ;
		visc_factor:coordinates = "lon_rho lat_rho" ;

// global attributes:
		:type = "ROMS GRID file" ;
		:title = "NJB grid for LEO-15" ;
}
