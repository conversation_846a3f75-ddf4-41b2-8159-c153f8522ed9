netcdf ini_ecosim {

dimensions:
	xi_rho = 43 ;
	xi_u = 42 ;
	xi_v = 43 ;
	xi_psi = 42 ;
	eta_rho = 82 ;
	eta_u = 82 ;
	eta_v = 81 ;
	eta_psi = 81 ;
	s_rho = 16 ;
	s_w = 17 ;
	tracer = 63 ;
	Nphy = 4 ;
	Nbac = 1 ;
	Ndom = 2 ;
	Nfec = 2 ;
	ocean_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
	double ocean_time(ocean_time) ;
		ocean_time:long_name = "time since initialization" ;
		ocean_time:units = "seconds since 1968-05-23 00:00:00 GMT" ;
		ocean_time:calendar = "gregorian" ;
	float zeta(ocean_time, eta_rho, xi_rho) ;
		zeta:long_name = "free-surface" ;
		zeta:units = "meter" ;
		zeta:time = "ocean_time" ;
		zeta:coordinates = "lon_rho lat_rho ocean_time" ;
	float ubar(ocean_time, eta_u, xi_u) ;
		ubar:long_name = "vertically integrated u-momentum component" ;
		ubar:units = "meter second-1" ;
		ubar:time = "ocean_time" ;
		ubar:coordinates = "lon_u lat_u ocean_time" ;
	float vbar(ocean_time, eta_v, xi_v) ;
		vbar:long_name = "vertically integrated v-momentum component" ;
		vbar:units = "meter second-1" ;
		vbar:time = "ocean_time" ;
		vbar:coordinates = "lon_v lat_v ocean_time" ;
	float u(ocean_time, s_rho, eta_u, xi_u) ;
		u:long_name = "u-momentum component" ;
		u:units = "meter second-1" ;
		u:time = "ocean_time" ;
		u:coordinates = "lon_u lat_u s_rho ocean_time" ;
	float v(ocean_time, s_rho, eta_v, xi_v) ;
		v:long_name = "v-momentum component" ;
		v:units = "meter second-1" ;
		v:time = "ocean_time" ;
		v:coordinates = "lon_v lat_v s_rho ocean_time" ;
	float temp(ocean_time, s_rho, eta_rho, xi_rho) ;
		temp:long_name = "potential temperature" ;
		temp:units = "Celsius" ;
		temp:time = "ocean_time" ;
		temp:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float salt(ocean_time, s_rho, eta_rho, xi_rho) ;
		salt:long_name = "salinity" ;
		salt:time = "ocean_time" ;
		salt:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DIC(ocean_time, s_rho, eta_rho, xi_rho) ;
		DIC:long_name = "DIC concentration" ;
		DIC:units = "millimole_DIC meter-3" ;
		DIC:time = "ocean_time" ;
		DIC:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float FeO(ocean_time, s_rho, eta_rho, xi_rho) ;
		FeO:long_name = "iron concentration" ;
		FeO:units = "millimole_FeO meter-3" ;
		FeO:time = "ocean_time" ;
		FeO:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float NH4(ocean_time, s_rho, eta_rho, xi_rho) ;
		NH4:long_name = "ammonium concentration" ;
		NH4:units = "millimole_NH4 meter-3" ;
		NH4:time = "ocean_time" ;
		NH4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float NO3(ocean_time, s_rho, eta_rho, xi_rho) ;
		NO3:long_name = "nitrate concentration" ;
		NO3:units = "millimole_N03 meter-3" ;
		NO3:time = "ocean_time" ;
		NO3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float PO4(ocean_time, s_rho, eta_rho, xi_rho) ;
		PO4:long_name = "phosphate concentration" ;
		PO4:units = "millimole_PO4 meter-3" ;
		PO4:time = "ocean_time" ;
		PO4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float SiO(ocean_time, s_rho, eta_rho, xi_rho) ;
		SiO:long_name = "silica concentration" ;
		SiO:units = "millimole_SiO meter-3" ;
		SiO:time = "ocean_time" ;
		SiO:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Bac_C1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Bac_C1:long_name = "bacteria, carbon group 1" ;
		Bac_C1:units = "millimole_C meter-3" ;
		Bac_C1:time = "ocean_time" ;
		Bac_C1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Bac_F1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Bac_F1:long_name = "bacteria, iron group 1" ;
		Bac_F1:units = "millimole_Fe meter-3" ;
		Bac_F1:time = "ocean_time" ;
		Bac_F1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Bac_N1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Bac_N1:long_name = "bacteria, nitrogen group 1" ;
		Bac_N1:units = "millimole_N meter-3" ;
		Bac_N1:time = "ocean_time" ;
		Bac_N1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Bac_P1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Bac_P1:long_name = "bacteria, phosphorus group 1" ;
		Bac_P1:units = "millimole_P meter-3" ;
		Bac_P1:time = "ocean_time" ;
		Bac_P1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float CDM_C1(ocean_time, s_rho, eta_rho, xi_rho) ;
		CDM_C1:long_name = "color degradational matter, carbon group 1" ;
		CDM_C1:units = "millimole_CDMC meter-3" ;
		CDM_C1:time = "ocean_time" ;
		CDM_C1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_C1(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_C1:long_name = "dissolved organic matter, carbon group 1" ;
		DOM_C1:units = "millimole_C meter-3" ;
		DOM_C1:time = "ocean_time" ;
		DOM_C1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_N1(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_N1:long_name = "dissolved organic matter, nitrogen group 1" ;
		DOM_N1:units = "millimole_N meter-3" ;
		DOM_N1:time = "ocean_time" ;
		DOM_N1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_P1(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_P1:long_name = "dissolved organic matter, phosphorus group 1" ;
		DOM_P1:units = "millimole_P meter-3" ;
		DOM_P1:time = "ocean_time" ;
		DOM_P1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float CDM_C2(ocean_time, s_rho, eta_rho, xi_rho) ;
		CDM_C2:long_name = "color degradational matter, carbon group 2" ;
		CDM_C2:units = "millimole_CDMC meter-3" ;
		CDM_C2:time = "ocean_time" ;
		CDM_C2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_C2(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_C2:long_name = "dissolved organic matter, carbon group 2" ;
		DOM_C2:units = "millimole_C meter-3" ;
		DOM_C2:time = "ocean_time" ;
		DOM_C2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_N2(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_N2:long_name = "dissolved organic matter, nitrogen group 2" ;
		DOM_N2:units = "millimole_N meter-3" ;
		DOM_N2:time = "ocean_time" ;
		DOM_N2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float DOM_P2(ocean_time, s_rho, eta_rho, xi_rho) ;
		DOM_P2:long_name = "dissolved organic matter, phosphorus group 2" ;
		DOM_P2:units = "millimole_P meter-3" ;
		DOM_P2:time = "ocean_time" ;
		DOM_P2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_C1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_C1:long_name = "fecal matter, carbon group 1" ;
		Fec_C1:units = "millimole_C meter-3" ;
		Fec_C1:time = "ocean_time" ;
		Fec_C1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_F1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_F1:long_name = "fecal matter, iron group 1" ;
		Fec_F1:units = "millimole_Fe meter-3" ;
		Fec_F1:time = "ocean_time" ;
		Fec_F1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_N1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_N1:long_name = "fecal matter, nitrogen group 1" ;
		Fec_N1:units = "millimole_N meter-3" ;
		Fec_N1:time = "ocean_time" ;
		Fec_N1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_P1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_P1:long_name = "fecal matter, phosphorus group 1" ;
		Fec_P1:units = "millimole_P meter-3" ;
		Fec_P1:time = "ocean_time" ;
		Fec_P1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_S1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_S1:long_name = "fecal matter, silica group 1" ;
		Fec_S1:units = "millimole_SiO meter-3" ;
		Fec_S1:time = "ocean_time" ;
		Fec_S1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_C2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_C2:long_name = "fecal matter, carbon group 2" ;
		Fec_C2:units = "millimole_C meter-3" ;
		Fec_C2:time = "ocean_time" ;
		Fec_C2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_F2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_F2:long_name = "fecal matter, iron group 2" ;
		Fec_F2:units = "millimole_Fe meter-3" ;
		Fec_F2:time = "ocean_time" ;
		Fec_F2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_N2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_N2:long_name = "fecal matter, nitrogen group 2" ;
		Fec_N2:units = "millimole_N meter-3" ;
		Fec_N2:time = "ocean_time" ;
		Fec_N2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_P2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_P2:long_name = "fecal matter, phosphorus group 2" ;
		Fec_P2:units = "millimole_P meter-3" ;
		Fec_P2:time = "ocean_time" ;
		Fec_P2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Fec_S2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Fec_S2:long_name = "fecal matter, silica group 2" ;
		Fec_S2:units = "millimole_SiO meter-3" ;
		Fec_S2:time = "ocean_time" ;
		Fec_S2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_C1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_C1:long_name = "small diatom, carbon group" ;
		Phy_C1:units = "millimole_C meter-3" ;
		Phy_C1:time = "ocean_time" ;
		Phy_C1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_F1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_F1:long_name = "small diatom, iron group" ;
		Phy_F1:units = "millimole_Fe meter-3" ;
		Phy_F1:time = "ocean_time" ;
		Phy_F1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_N1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_N1:long_name = "small diatom, nitrogen group" ;
		Phy_N1:units = "millimole_N meter-3" ;
		Phy_N1:time = "ocean_time" ;
		Phy_N1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_P1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_P1:long_name = "small diatom, phosphorus group" ;
		Phy_P1:units = "millimole_P meter-3" ;
		Phy_P1:time = "ocean_time" ;
		Phy_P1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_C2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_C2:long_name = "large diatom, carbon group" ;
		Phy_C2:units = "millimole_C meter-3" ;
		Phy_C2:time = "ocean_time" ;
		Phy_C2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_F2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_F2:long_name = "large diatom, iron group" ;
		Phy_F2:units = "millimole_Fe meter-3" ;
		Phy_F2:time = "ocean_time" ;
		Phy_F2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_N2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_N2:long_name = "large diatom, nitrogen group" ;
		Phy_N2:units = "millimole_N meter-3" ;
		Phy_N2:time = "ocean_time" ;
		Phy_N2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_P2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_P2:long_name = "large diatom, phosphorus group" ;
		Phy_P2:units = "millimole_P meter-3" ;
		Phy_P2:time = "ocean_time" ;
		Phy_P2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_C3(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_C3:long_name = "large dinoflagellate, carbon group" ;
		Phy_C3:units = "millimole_C meter-3" ;
		Phy_C3:time = "ocean_time" ;
		Phy_C3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_F3(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_F3:long_name = "large dinoflagellate, iron group" ;
		Phy_F3:units = "millimole_Fe meter-3" ;
		Phy_F3:time = "ocean_time" ;
		Phy_F3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_N3(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_N3:long_name = "large dinoflagellate, nitrogen group" ;
		Phy_N3:units = "millimole_N meter-3" ;
		Phy_N3:time = "ocean_time" ;
		Phy_N3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_P3(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_P3:long_name = "large dinoflagellate, phosphorus group" ;
		Phy_P3:units = "millimole_P meter-3" ;
		Phy_P3:time = "ocean_time" ;
		Phy_P3:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_C4(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_C4:long_name = "synechococcus, carbon group" ;
		Phy_C4:units = "millimole_C meter-3" ;
		Phy_C4:time = "ocean_time" ;
		Phy_C4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_F4(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_F4:long_name = "synechococcus, iron group" ;
		Phy_F4:units = "millimole_Fe meter-3" ;
		Phy_F4:time = "ocean_time" ;
		Phy_F4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_N4(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_N4:long_name = "synechococcus, nitrogen group" ;
		Phy_N4:units = "millimole_N meter-3" ;
		Phy_N4:time = "ocean_time" ;
		Phy_N4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_P4(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_P4:long_name = "synechococcus, phosphorus group" ;
		Phy_P4:units = "millimole_P meter-3" ;
		Phy_P4:time = "ocean_time" ;
		Phy_P4:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_S1(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_S1:long_name = "small diatom, silica group" ;
		Phy_S1:units = "millimole_SiO meter-3" ;
		Phy_S1:time = "ocean_time" ;
		Phy_S1:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Phy_S2(ocean_time, s_rho, eta_rho, xi_rho) ;
		Phy_S2:long_name = "large diatom, silica group" ;
		Phy_S2:units = "millimole_SiO meter-3" ;
		Phy_S2:time = "ocean_time" ;
		Phy_S2:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_11(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_11:long_name = "small diatom, chlorophyll-a" ;
		Pigs_11:units = "milligrams meter-3" ;
		Pigs_11:time = "ocean_time" ;
		Pigs_11:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_21(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_21:long_name = "large diatom, chlorophyll-a" ;
		Pigs_21:units = "milligrams meter-3" ;
		Pigs_21:time = "ocean_time" ;
		Pigs_21:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_31(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_31:long_name = "large dinoflagellate, chlorophyll-a" ;
		Pigs_31:units = "milligrams meter-3" ;
		Pigs_31:time = "ocean_time" ;
		Pigs_31:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_41(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_41:long_name = "synechococcus, chlorophyll-a" ;
		Pigs_41:units = "milligrams meter-3" ;
		Pigs_41:time = "ocean_time" ;
		Pigs_41:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_13(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_13:long_name = "small diatom, chlorophyll-c" ;
		Pigs_13:units = "milligrams meter-3" ;
		Pigs_13:time = "ocean_time" ;
		Pigs_13:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_23(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_23:long_name = "large diatom, chlorophyll-c" ;
		Pigs_23:units = "milligrams meter-3" ;
		Pigs_23:time = "ocean_time" ;
		Pigs_23:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_33(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_33:long_name = "large dinoflagellate, chlorophyll-c" ;
		Pigs_33:units = "milligrams meter-3" ;
		Pigs_33:time = "ocean_time" ;
		Pigs_33:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_14(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_14:long_name = "small diatom, photosythetic carotenoids" ;
		Pigs_14:units = "milligrams meter-3" ;
		Pigs_14:time = "ocean_time" ;
		Pigs_14:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_24(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_24:long_name = "large diatom, photosythetic carotenoids" ;
		Pigs_24:units = "milligrams meter-3" ;
		Pigs_24:time = "ocean_time" ;
		Pigs_24:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_34(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_34:long_name = "large dinoflagellate, photosythetic carotenoids" ;
		Pigs_34:units = "milligrams meter-3" ;
		Pigs_34:time = "ocean_time" ;
		Pigs_34:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_15(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_15:long_name = "small diatom, photoprotective carotenoids" ;
		Pigs_15:units = "milligrams meter-3" ;
		Pigs_15:time = "ocean_time" ;
		Pigs_15:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_25(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_25:long_name = "large diatom, photoprotective carotenoids" ;
		Pigs_25:units = "milligrams meter-3" ;
		Pigs_25:time = "ocean_time" ;
		Pigs_25:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_35(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_35:long_name = "large dinoflagellate, photoprotective carotenoids" ;
		Pigs_35:units = "milligrams meter-3" ;
		Pigs_35:time = "ocean_time" ;
		Pigs_35:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_45(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_45:long_name = "synechococcus, photoprotective carotenoids" ;
		Pigs_45:units = "milligrams meter-3" ;
		Pigs_45:time = "ocean_time" ;
		Pigs_45:coordinates = "lon_rho lat_rho s_rho ocean_time" ;
	float Pigs_47(ocean_time, s_rho, eta_rho, xi_rho) ;
		Pigs_47:long_name = "synechococcus, high urobilin phycoeurythin carotenoids" ;
		Pigs_47:units = "milligrams meter-3" ;
		Pigs_47:time = "ocean_time" ;
		Pigs_47:coordinates = "lon_rho lat_rho s_rho ocean_time" ;

// global attributes:
		:type = "ROMS INITIAL file" ;
		:title = "ROMS Initial fiels for Hydrodynamics and EcoSim" ;
		:grd_file = "roms_grd.nc" ;

}
