############################################################################
#
#  Program:         ARPACK
#
#  Module:          ARmake.inc
#
#  Purpose:         Top-level Definitions
#
#  Creation date:   February 22, 1996
#
#  Modified:
#
#  Send bug reports, comments or <NAME_EMAIL>
#
############################################################################
#
#
#  *** If you are using a VENDOR SUPPLIED VERSION of MPI, you    ***
#  *** must replace the mpif.h in the following directories      ***
#  ***                                                           ***
#  ***            ARPACK/PARPACK/SRC/MPI/mpif.h                  ***
#  ***            ARPACK/PARPACK/UTIL/MPI/mpif.h                 ***
#  ***            ARPACK/PARPACK/EXAMPLES/MPI/mpif.h             ***
#  ***                                                           ***
#  *** with the one for the native implementation.               ***
#
#  The complete path to the top level of ARPACK directory, usually
#  $(HOME)/ARPACK
#
#home          = $(HOME)/ARPACK
home          = /home1/Netlib/ARPACK
#
#  The platform identifier to suffix to the end of library names
#
PLAT          = CJ
#
#  The communication library ( need for parallel library only )
#  Current availible platforms are: BLACS or MPI
#
COMMLIB       = MPI
#
#  The directories to find the various pieces of ARPACK
#
BLASdir      = $(home)/BLAS
LAPACKdir    = $(home)/LAPACK
UTILdir      = $(home)/UTIL
SRCdir       = $(home)/SRC
PSRCdir      = $(home)/PARPACK/SRC/$(COMMLIB)
PUTILdir     = $(home)/PARPACK/UTIL/$(COMMLIB)
#
#DIRS   = $(BLASdir) $(LAPACKdir) $(UTILdir) $(SRCdir)
# Comment out the previous line and uncomment the following
# if you already have the BLAS and LAPACK installed on your system.
# NOTE: ARPACK assumes the use of LAPACK version 2 codes.
DIRS   = $(UTILdir) $(SRCdir)
#
#
#  The name of the libraries to be created/linked to
#
ARPACKLIB  = $(home)/libarpack_$(PLAT).a
PARPACKLIB = $(home)/parpack_$(COMMLIB)-$(PLAT).a
LAPACKLIB = /home1/Netlib/LAPACK/lapack_$(PLAT).a
BLASLIB = /home1/Netlib/LAPACK/blas_$(PLAT).a

ALIBS =  $(ARPACKLIB) $(LAPACKLIB) $(BLASLIB) 
#

# Libraries needed for Parallel ARPACK - MPI 
#
MPILIBS = 

PLIBS = $(PARPACKLIB) $(ALIBS) $(MPILIBS)



#  Make our own suffixes' list.
#
.SUFFIXES:
.SUFFIXES:	.f	.o
.SUFFIXES:	.F	.f

#
#  Default command.
#
.DEFAULT:
	@$(ECHO) "Unknown target $@, try:  make help"

#
#  Command to build .o files from .f files.
#
.f.o:
	@$(ECHO) Making $@ from $<
	@$(FC) -c $(FFLAGS) $<

.F.f:
	@$(ECHO) Making $*.f from $<
	@$(CPP) -P -DSINGLE $(CPPFLAGS) $< $*.f
	@$(ECHO) Making d$*.f from $<
	@$(CPP) -P -DDOUBLE $(CPPFLAGS) $< d$*.f

#
#  Various compilation programs and flags.
#  You need to make sure these are correct for your system.
#
FC		= f77
FFLAGS	= -Kmips2 -Kopt=2 -Zstatic

# Set the parallel fortran compiler
PFC     = mpif77
PFFLAGS	= -Kmips2 -Kopt=2 -Zstatic

LDFLAGS = 
CD		= cd
AR      = ar

CHMOD	 = chmod
CHFLAGS	 = -f

COMPRESS = compress

CP	 = cp

#  To find the path for cpp, try `man cpp', and it should list the path
#  at the top, under `Syntax'.  It is usually in /lib.
CPP	 = /lib/cpp
CPPFLAGS =

ECHO	 = echo

LN	 = ln
LNFLAGS	 = -s

MAKE	 = /bin/make

MKDIR	 = mkdir
MDFLAGS	 = -p

MV	 = mv
MVFLAGS	 = -f

RM	 = rm
RMFLAGS  = -f

SHELL	 = /bin/sh

TAR	 = tar

#  For System V based machine without ranlib, like Cray and SGI, 
#  use touch instead.
RANLIB   = touch

#
#  This is the general help target.
#
help:
	@$(ECHO) "usage: make ?"

