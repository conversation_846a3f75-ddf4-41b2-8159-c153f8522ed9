!
! Multiple model coupling parameters: ESMF/NUOPC Library
!
!svn $Id: coupling_esmf.in 1054 2021-03-06 19:47:12Z arango $
!========================================================= Hernan G. Arango ===
!  Copyright (c) 2002-2021 The ROMS/TOMS Group                                !
!    Licensed under a MIT/X style license                                     !
!    See License_ROMS.txt                                                     !
!==============================================================================
!                                                                             !
! Input parameters can be entered in ANY order, provided that the parameter   !
! KEYWORD (usually, upper case) is typed correctly followed by "="  or "=="   !
! symbols. Any comment lines are allowed and must begin with an exclamation   !
! mark (!) in column one.  Comments may  appear to the right of a parameter   !
! specification to improve documentation.  Comments will be ignored  during   !
! reading.  Blank lines are also allowed and ignored. Continuation lines in   !
! a parameter specification are allowed and must be preceded by a backslash   !
! (\).  In some instances, more than one value is required for a parameter.   !
! If fewer values are provided, the  last value  is assigned for the entire   !
! parameter array.  The multiplication symbol (*),  without blank spaces in   !
! between, is allowed for a parameter specification.  For example, in a two   !
! grids nested application:                                                   !
!                                                                             !
!    AKT_BAK == 2*1.0d-6  2*5.0d-6              ! m2/s                        !
!                                                                             !
! indicates that the first two entries of array AKT_BAK,  in fortran column-  !
! major order, will have the same value of "1.0d-6" for grid 1,  whereas the  !
! next two entries will have the same value of "5.0d-6" for grid 2.           !
!                                                                             !
! In multiple levels of nesting and/or multiple connected domains  step-ups,  !
! "Ngrids" entries are expected for some of these parameters.  In such case,  !
! the order of the entries for a parameter is extremely important.  It  must  !
! follow the same order (1:Ngrids) as in the state variable declaration. The  !
! USER may follow the above guidelines for specifying his/her values.  These  !
! parameters are marked by "==" plural symbol after the KEYWORD.              !
!                                                                             !
! If the DATA Model is activated, the user needs to specify all the filenames !
! containing export fields required by a particular ESM component. Currently, !
! multiple NetCDF files are allowed for export fields. Often, it is useful to !
! split import data time records into several files (say monthly, annual, and !
! so on). In this case, each multiple filename entry lines need to end with   !
! the vertical bar (|) symbol. For example:                                   !
!                                                                             !
! nDataFiles(seaice) == 5                ! number of NetCDF files to process  !
!                                                                             !
!  DataFiles(seaice) == my_swrad_year1.nc   |                                 !
!                       my_swrad_year2.nc   \                                 !
!                       my_wstress_year1.nc |                                 !
!                       my_wstress_year2.nc \                                 !
!                       my_Pair_year1.nc    |                                 !
!                       my_Pair_year2.nc    \                                 !
!                       my_Qair_year1.nc    |                                 !
!                       my_Qair_year2.nc    \                                 !
!                       my_Tair_year1.nc    |                                 !
!                       my_Tair_year2.nc                                      !
!                                                                             !
! Notice that "nDataFiles(seaice)" is 5 and not 10. There are 5 uniquely      !
! different fields in the file list; we DO NOT count entries followed by      !
! the vertical bar (|) symbol. Multiple file entries are processed with       !
! derived type structures.                                                    !
!                                                                             !
!==============================================================================
!
! Coupling import/export variables metadata filename.

           CPLname = coupling_esmf.dat

! Coupling free-format run sequence configuration filename.

          CONFname = esmApp.runconfig

! Coupled models standard input script or namelist filename.

   INPname(roms)   = roms.in
   INPname(atmos)  = atmos.in
   INPname(seaice) = seaice.in
   INPname(waves)  = waves.in

! Active coupled Earth System Models (ESM) switch (T/F).

  IsActive(roms)   = T                      ! ROMS
  IsActive(atmos)  = F                      ! atmosphere model
  IsActive(seaice) = F                      ! seaice model
  IsActive(waves)  = F                      ! wave model
  IsActive(data)   = T                      ! DATA model

! Set which ESM components are connected to the ocean component (ROMS)
! during coupling (T/F). If ROMS has nested grids, indicate which grids
! are connected to the other ESM components, [1:NgridsR] expected.

  Coupled(ATM2OCN) ==  F                    ! atmosphere -> ROMS connected
  Coupled(ICE2OCN) ==  F                    ! seaice -> ROMS connected
  Coupled(WAV2OCN) ==  F                    ! wave -> ROMS connected
  Coupled(DAT2OCN) ==  T                    ! DATA -> ROMS connected

! Set which ESM components are connected to the atmosphere component
! during coupling (T/F). If the atmosphere component has nested grids,
! indicate which grids are connected to the other ESM components,
! [1:NgridsA] expected.

  Coupled(OCN2ATM) ==  F                    ! ROMS -> atmosphere connected
  Coupled(ICE2ATM) ==  F                    ! seaice -> atmosphere connected
  Coupled(WAV2ATM) ==  F                    ! wave -> atmosphere connected
  Coupled(DAT2ATM) ==  F                    ! DATA -> atmosphere connected

! Set which ESM components are connected to the seaice component
! during coupling (T/F). If the seaice component has nested grids,
! indicate which grids are connected to the other ESM components,
! [1:NgridsI] expected.

  Coupled(ATM2ICE) ==  F                    ! atmosphere -> seaice connected
  Coupled(OCN2ICE) ==  F                    ! ROMS -> seaice connected
  Coupled(WAV2ICE) ==  F                    ! wave -> seaice connected
  Coupled(DAT2ICE) ==  F                    ! DATA -> seaice connected

! Set which ESM components are connected to the wave component
! during coupling (T/F). If the wave component has nested grids,
! indicate which grids are connected to the other ESM components,
! [1:NgridsW] expected.

  Coupled(ATM2WAV) ==  F                    ! atmosphere -> wave connected
  Coupled(ICE2WAV) ==  F                    ! seaice -> wave connected
  Coupled(OCN2WAV) ==  F                    ! ROMS -> wave connected
  Coupled(DAT2WAV) ==  F                    ! DATA -> wave connected

! Model coupling type (check Glossary):
!
!    1:   Explicit
!    2:   Semi-Implicit.

      CouplingType = 1

!  Persistent Execution Thread (PET) layout option:
!
!    0:   Sequential, coupled models executed on all the specified PETs
!    1:   Concurrent, coupled models executed on non-overlapping set of PETs

   PETlayoutOption = 0

! DATA Model parallel distributed-memory domain decomposition.

            ItileD = 2                      ! I-direction partition
            JtileD = 2                      ! J-direction partition

! Number of PETs assigned to each model in the coupled system. If sequential
! layout, assign the same number of PETs to all coupled models. If concurrent
! layout, the number of PETs needed is the sum of all coupled model components.
! Only the PET values for IsActive(...)=T are processed and considered.

  Nthreads(roms)   =  4                     ! ocean model PETs
  Nthreads(atmos)  =  1                     ! atmosphere model PETs
  Nthreads(seaice) =  1                     ! seaice model PETs
  Nthreads(waves)  =  1                     ! wave model PETs

! Set ESM coupling driver clock. If not re-starting, set StartTime and
! RestartTime to the same values.  Set coupling time interval (TimeStep)
! to the largest value for the field exchange between activated ESM
! components. Check glossary below for more information.
!
!                     YYYY MM DD hh mm ss

     ReferenceTime =  2006 01 01 00 00 00   ! Simulation reference time
         StartTime =  2014 01 01 00 00 00   ! Simulation start time
       RestartTime =  2014 01 01 00 00 00   ! Simulation restart time
          StopTime =  2014 01 06 00 00 00   ! Simulation stop time
          TimeStep =  0000 00 00 03 00 00   ! Coupler driver interval

          Calendar =  gregorian

! Coupling time interval fraction (INTEGER) from driver TimeStep indicating
! how often the exchange of information between ESM components occurs. That
! is, the coupling interval between ESM components is TimeStep/TimeFrac.
! If coupling nested grids, specify the respective number of values.
! Only active model components are processed and considered.

 TimeFrac(ATM2OCN) ==  1                    ! atmosphere -> ROMS coupling, NgridsR
 TimeFrac(ICE2OCN) ==  1                    ! seaice -> ROMS coupling, NgridsR
 TimeFrac(WAV2OCN) ==  1                    ! wave -> ROMS coupling, NgridsR
 TimeFrac(DAT2OCN) ==  1                    ! DATA -> ROMS coupling, NgridsR

 TimeFrac(OCN2ATM) ==  1                    ! ROMS -> atmosphere coupling, NgridsA
 TimeFrac(ICE2ATM) ==  1                    ! seaice -> atmosphere coupling, NgridsA
 TimeFrac(WAV2ATM) ==  1                    ! wave -> atmosphere coupling, NgridsA
 TimeFrac(DAT2ATM) ==  1                    ! DATA -> atmosphere coupling, NgridsA

 TimeFrac(ATM2ICE) ==  1                    ! atmosphere -> seaice coupling, NgridsI
 TimeFrac(OCN2ICE) ==  1                    ! ROMS -> seaice coupling, NgridsI
 TimeFrac(WAV2ICE) ==  1                    ! wave -> seaice coupling, NgridsI
 TimeFrac(DAT2ICE) ==  1                    ! DATA -> seaice coupling, NgridsI

 TimeFrac(ATM2WAV) ==  1                    ! atmosphere -> wave coupling, NgridsW
 TimeFrac(ICE2WAV) ==  1                    ! seaice -> wave coupling, NgridsW
 TimeFrac(OCN2WAV) ==  1                    ! ROMS -> wave coupling, NgridsW
 TimeFrac(DAT2WAV) ==  1                    ! DATA -> wave coupling, NgridsW

! Internal ESMF parameters (see below for information).

   extrapNumLevels =  4     ! number of levels for creep fill extrapolation

! Weighted coefficients for the merging of DATA component fields with the same
! field from other ESM components. Melding coefficients are positive and MUST
! add to unity. They are read from an input NetCDF file ('WeightsFile'). The
! user needs to specify the NetCDF variable names for the weights for the DATA
! ('VnameDATA') and ESM ('VnameESM') components. Also, specify which grid
! ('NestedGrid') needs a merged field from DATA and ESM components. Currently,
! the weight values are only needed by the atmosphere component.

WeightsFile(atmos) =  meld_weights_atm.nc
  VnameDATA(atmos) =  data_weight
   VnameESM(atmos) =  ocean_weight
 NestedGrid(atmos) =  1

! Coupling debug flag:      [0] no debugging
!                           [1] reports informative messages
!                           [2] <1> and coupled components RunSequence
!                           [3] <2> and writes exchange fields into NetCDF files
!                           [4] <3> and writes grid information in VTK format
!
        DebugLevel = 1

! Execution trace flag:     [0] no tracing
!                           [1] reports the sequence of coupling subroutine calls
!                           [2] <1> writes voluminous ESMF library tracing information

        TraceLevel = 0

! Export/Import fields: Use the following string codes to set the fields
!                       to export/import for each coupled model.
!
!                       (*) Computed from an exchanged field, not regridded
!
! Field       Export  Import
!
! NONE        -       -                  No field to import or export
!
! shflx       atmos   roms               surface net heat flux
! cloud       atmos   roms               cloud fraction
! Hair        atmos   roms, cice         surface specific humidity
! Tair        atmos   roms, cice         surface air temperature
! Pair        atmos   roms, cice         surface air pressure
! Pmsl        atmos   roms               mean sea level pressure
! Qair        atmos   roms               surface relative humidity
! SWrad       atmos   roms               shortwave radiation flux
! SWrad_daily atmos   roms               daily averaged shortwave radiation flux
! LWrad       atmos   roms               surface net longwave radiation flux
! dLWrad      atmos   roms               surface downward longwave radiation flux
! rain        atmos   roms               rain fall rate
! Uwind       atmos   roms, cice, waves  surface U-wind component
! Vwind       atmos   roms, cice, waves  surface V-wind component
! Ustr        atmos   roms, cice         surface U-momentum stress
! Vstr        atmos   roms, cice         surface V-momentum stress
!
! zlvl        atmos   cice               atmspheric height lowest level
! rhoa        atmos   cice               surface air density
! potT        atmos   cice             * surface air potential temperature
! flw         atmos   cice               mean downwelling longwave flux
! swvdr       atmos   cice               visible direct downward shortwave flux
! swvdf       atmos   cice               visible diffusive downward shortwave flux
! swidr       atmos   cice               infrared direct downward shortwave flux
! swidf       atmos   cice               infrared diffusive downward shortwave flux
! frain       atmos   cice               mean liquid precipitation rate
! fsnow       atmos   cice               mean frozen/snow precipitation rate
! aero_atm    atmos   cice               meam aerosol deposition rate
!
! ifrac       cice    atmos              fractional ice area
! vice        cice    atmos              ice volume per unit area
! vsno        cice    atmos              snow volume per unit area
! sit         cice    atmos              surface ice/snow temperature
! alvdr       cice    atmos              fractional visible band direct albedo
! alvdf       cice    atmos              fractional visible band diffusive albedo
! alidr       cice    atmos              fractional near-infrared band direct albedo
! alidf       cice    atmos              fractional near-infrared band diffusive albedo
! strairxT    cice    atmos              zonal stress on ice by air
! strairyT    cice    atmos              meridional stress on ice by air
! fsens       cice    atmos              ice sensible heat flux
! flat        cice    atmos              ice latent heat flux
! evap        cice    atmos              mean evaporative water flux
! flwout      cice    atmos              mean outgoing upward longwave radiation
!
! fhocn       cice    roms               net heat flux to ocean
! fresh       cice    roms               fresh water flux to ocean
! fpond       cice    roms               fresh water flux to ice ponds
! fsalt       cice    roms               salt flux to ocean
! strocnxT    cice    roms               zonal stress on ice by ocean
! strocnyT    cice    roms               meridional stress on ice by ocean
! fswthru     cice    roms               shortwave flux, ice to ocean
! fswthruvdr  cice    roms               visible direct band net shortwave, ice to ocean
! fswthruvdf  cice    roms               visible diffusive band net shortwave, ice to ocean
! fswthruidr  cice    roms               infrared direct band net shortwave, ice to ocean
! fswthruidf  cice    roms               infrared diffusive band net shortwave, ice to ocean
!
! frzmlt      roms    cice               freezing/melting potential
! hmix        roms    cice               mixed layer depth
! SSH         roms    cice,  waves       sea surface height
! SST         roms    atmos, cice        sea surface temperature
! SSS         roms    cice               sea surface salinity
! ss_tltx     roms    cice             * sea surface slope, x-direction
! ss_tlty     roms    cice             * sea surface slope, y-direction'
! Tf          roms    cice               freezing temperature
! Ubar        roms    waves              vertically integrated U-momentum
! Vbar        roms    waves              vertically integrated V-momentum
! Usur        roms    cice               zonal surface ocean curent
! Vsur        roms    cice               meridional surface ocean curent
! ZO          roms    waves              bottom roughness
!
! Wdir        waves   roms               wave direction
! Wamp        waves   roms               significant wave height
! Wlen        waves   roms               average wave length
! Wptop       waves   roms               surface wave relative peak period
! Wpbot       waves   roms               bottom wave period
! Wdiss       waves   roms               wave energy dissipation
! Wbrk        waves   roms               percent wave breaking
! Wubot       waves   roms               wave bottom orbital velocity

! Ocean model (ROMS) Import and Export fields: Import(1:Nimport) and
! Export(1:Nexport).

     Nimport(roms) = 8
     Nexport(roms) = 0

      Import(roms) = dLWrad \
                     SWrad  \
                     Pair   \
                     Tair   \
                     Qair   \
                     rain   \
                     Uwind  \
                     Vwind

      Export(roms) = NONE

! Atmospheric model Import and Export fields: Import(1:Nimport) and
! Export(1:Nexport).

    Nimport(atmos) = 0
    Nexport(atmos) = 0

     Import(atmos) = NONE

     Export(atmos) = NONE

! Sea-ice model Import and Export fields: Import(1:Nimport) and
! Export(1:Nexport).

   Nimport(seaice) = 0
   Nexport(seaice) = 0

    Import(seaice) = NONE

    Export(seaice) = NONE

! Sea-ice model Import and Export fields: Import(1:Nimport) and
! Export(1:Nexport).

    Nimport(waves) = 0
    Nexport(waves) = 0

     Import(waves) = NONE

     Export(waves) = NONE

!::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
!
! DATA Model Set-up
!
! For each ESM component, specify the following information:
!
!   nDataExport(...)          The number of fields to export from DATA Model
!                             to specified ESM component. If zero value,
!                             exporting of fields to ESM component do not
!                             take place and the values in "DataExport" and
!                             "DataFiles" are ignored.
!
!    nDataFiles(...)          The number of NetCDF files that the DATA Model
!                             needs to process and read exporting fields.
!
!    DataExport(...)          Field short name string (see above table) to
!                             read and export to the specified ESM component,
!                             "nDataExport" values are expected for activated
!                             ESM component.
!
!     DataFiles(...)          NetCDF filenames containing the field to export
!                             to specified ESM component, nDataFiles are
!                             expected. The order of files in the list is
!                             essential. See information about multi-files
!                             below.
!
! Currently, the DATA model only EXPORTS fields. It is improbable that the
! DATA needs to import fields to itself.
!
! If applicable, the USER has the option to enter several sets of filenames
! for each nested grid.  Alternatively, if all the export fields are the same
! for each nesting grid, and the data is in its native resolution, we could
! enter only one set of filenames, and the DATA model will replicate those
! files internally to the remaining grids using the plural KEYWORD protocol.
! The USER may have different filenames for each export field.
!
! The DATA model will scan the files and will read the needed data from the
! first file in the list containing the export field. Therefore, the order of
! the filenames is critical. If using multiple files per nested grid, first
! enter all the filenames for grid one followed by two, and so on. It is also
! possible to split input data time records into several files (see Prolog
! instructions above). Use a single line per entry with a continuation (\)
! or a vertical bar (|) symbol after each entry, except the last one.
!
!::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

!------------------------------------------------------------------------------
! DATA model export to ROMS.
!------------------------------------------------------------------------------

  nDataExport(roms) = 8                 ! number of fields to export

   nDataFiles(roms) = 8                 ! number of NetCDF files to process

   DataExport(roms) = dLWrad \
                      SWrad  \
                      Pair   \
                      Tair   \
                      Qair   \
                      rain   \
                      Uwind  \
                      Vwind

    DataFiles(roms) = ../om/lwrad_down_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/lwrad_down_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/lwrad_down_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/swrad_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/swrad_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/swrad_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/Pair_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/Pair_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/Pair_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/Tair_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/Tair_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/Tair_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/Qair_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/Qair_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/Qair_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/rain_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/rain_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/rain_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/Uwind_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/Uwind_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/Uwind_nam_3hourly_MAB_and_GoM_2016.nc \

                      ../om/Vwind_nam_3hourly_MAB_and_GoM_2014.nc |
                      ../om/Vwind_nam_3hourly_MAB_and_GoM_2015.nc |
                      ../om/Vwind_nam_3hourly_MAB_and_GoM_2016.nc

!------------------------------------------------------------------------------
! DATA model export to the Atmosphere Model.
!------------------------------------------------------------------------------

 nDataExport(atmos) = 0                 ! number of fields to export

  nDataFiles(atmos) = 0                 ! number of NetCDF files to process

  DataExport(atmos) = NONE

   DataFiles(atmos) = data2atm.nc

!------------------------------------------------------------------------------
! DATA model Export to the Sea Ice Model.
!------------------------------------------------------------------------------

nDataExport(seaice) = 0                 ! number of fields to export

 nDataFiles(seaice) = 0                 ! number of NetCDF files to process

 DataExport(seaice) = NONE

  DataFiles(seaice) = data2ice.nc

!------------------------------------------------------------------------------
! DATA model Export to the Wave Model.
!------------------------------------------------------------------------------

 nDataExport(waves) = 0                 ! number of fields to export

  nDataFiles(waves) = 0                 ! number of NetCDF files to process

  DataExport(waves) = NONE

   DataFiles(waves) = data2wav.nc


!
!==============================================================================
!  GLOSSARY:
!==============================================================================
!
!  Currently, coupling is only possible with four Earth System model (ESM)
!  components: ocean, atmosphere, sea-ice, wave models.  Other type models
!  added by writing the respective CAP file. Some of the KEYWORD entries
!  are expected with generic component label in parenthesis:
!
!    Keyword(roms)        Always a component  ROMS
!    Keyword(atmos)       atmosphere model    COAMPS, RegCM, WRF
!    Keyword(seaice)      sea-ice model       CICE
!    Keyword(waves)       wave model          RED/DIF, SWAN, WAM
!    Keyword(data)        data model
!
!------------------------------------------------------------------------------
! Input filenames.
!------------------------------------------------------------------------------
!
! CPL_name       Coupling import/export variables metadata filename.
!
! CONFname       Coupling free-format run sequence configuration filename.
!
! INPname        Coupled models components standard input script or namelist
!                  filenames:
!
!                  INPname(roms)       ROMS input script
!                  INPname(atmos)      Atmosphere model input script
!                  INPname(seaice)     Sea-ice model input script
!                  INPname(waves)      Wave model input script
!
!------------------------------------------------------------------------------
! Coupled model parameters.
!------------------------------------------------------------------------------
!
! IsActive         Logical switch (T/F) indicating if an Earth System Model
!                    (ESM) component is active or not. It is also used to
!                    process some coupling parameters specified in this
!                    input script.
!
!                    IsActive(roms)    Activate coupling to ROMS. If nesting,
!                                      enter NgridsR values. The first value
!                                      must be TRUE always. The user has the
!                                      choice to activate or not a particular
!                                      nested grid. By the default, NgridsR=1.
!
!                    IsActive(atmos)   Activate coupling to atmosphere model.
!                                      If nesting, enter NgridsA values. The
!                                      first value must be TRUE always. The
!                                      user has the choice to activate or not
!                                      a particular nested grid. By default,
!                                      NgridsA=1.
!
!                    IsActive(seaice)  Activate coupling to sea-ice model.
!                                      If nesting, enter NgridsI values. The
!                                      first value must be TRUE always. The
!                                      user has the choice to activate or not
!                                      a particular nested grid. By default,
!                                      NgridsI=1.
!
!                    IsActive(waves)   Activate coupling to wave model. If
!                                      If nesting, enter NgridsW values. The
!                                      first value must be TRUE always. The
!                                      user has the choice to activate or not
!                                      a particular nested grid. By default,
!                                      NgridsW=1.
!
!                    IsActive(data)    Activate coupling to a DATA model. It is
!                                      used to provide data to ESM components
!                                      uncoupled fields or to provide values at
!                                      locations not imported because of
!                                      noncoincident grids.
!
!                                      For example:
!
!                                        If just coupling ROMS-CICE, the DATA
!                                        model provides all the atmosphere
!                                        forcing fields required by CICE.
!
!                                        If atmosphere and ocean models grids
!                                        are not coincident, the DATA model
!                                        provides SST on locations not covered
!                                        by the usually smaller ocean grid.
!
!                  ESMF/NUOPC does not provide an elegant and straight way to
!                  coupled nested grids. The current design is to treat each
!                  nested grid as a unique coupled ESM component (see
!                  esmf_esm.F, esmf_roms.F, etc).
!
!                  If nesting, provide as many values to IsActive(xxxx) as
!                  needed for each nested grid. The order of the values is
!                  important. If only one value is set, only the application
!                  main grid is coupled.
!
! Coupled          Logical switch indicating which ESM components are connected
!                    during coupling (T/F). If the ESM component has nested
!                    grids, it specifies which ones are connected to other
!                    components.
!
!                    Components connected to ROMS, [1:NgridsR] expected:
!
!                      Coupled(ATM2OCN)       atmosphere -> ROMS
!                      Coupled(ICE2OCN)       seaice -> ROMS
!                      Coupled(WAV2OCN)       wave -> ROMS
!                      Coupled(DAT2OCN)       DATA -> ROMS
!
!                    Components connected to ATMOSPHERE, [1:NgridsA] expected:
!
!                      Coupled(OCN2ATM)       ROMS -> atmosphere
!                      Coupled(ICE2ATM)       seaice -> atmosphere
!                      Coupled(WAV2ATM)       wave -> atmosphere
!                      Coupled(DAT2ATM)       DATA -> atmosphere
!
!                    Components connected to SEAICE [1:NgridsI] expected:
!
!                      Coupled(ATM2ICE)       atmosphere -> seaice
!                      Coupled(OCN2ICE)       ROMS -> seaice
!                      Coupled(WAV2ICE)       wave -> seaice
!                      Coupled(DAT2ICE)       DATA -> seaice
!
!                    Components connected to WAVE [1:NgridsW] expected:
!
!                      Coupled(ATM2WAV)       atmosphere -> wave
!                      Coupled(ICE2WAV)       seaice -> wave
!                      Coupled(OCN2WAV)       ROMS -> wave
!                      Coupled(DAT2WAV)       data -> wave
!
! CouplingType     Model coupling type (check Glossary):
!
!                    CouplingType = 1    Explicit coupling
!                    CouplingType = 2    Semi-Implicit coupling
!
!                    In explicit coupling, the exchange fields at the next
!                    time-step (n+1) are defined using known values from the
!                    time-step (n) before it. Explicit methods require less
!                    computational effort and are accurate for small coupling
!                    time-steps.
!
!                    In implicit coupling, the exchange fields at the next
!                    time-step (n+1) are defined by including values at the
!                    next time-step (n+1). Implicit methods are stable and
!                    allow longer coupling time-steps but are expensier.
!
!                    In semi-implicit coupling, the exchange in one direction
!                    is explicit and in the reverse direction is implicit. For
!                    example:
!
!                      ROMS -> ATM           Explicit exchange
!                      ATM  -> ROMS          Implicit exchange
!
! PETlayoutOption  Persistent Execution Thread (PET) layout option;
!
!                    PETlayoutOption = 0   Sequential, coupled models are
!                                          executed on all the specified PETs
!
!                    PETlayoutOption = 1   Concurrent, coupled models are
!                                          executed on non-overlapping set of
!                                          PETs
!
! ItileD             Number of domain partitions in the I-direction (lon axis).
!                    It must be equal to or greater than one.
!
! JtileD             Number of domain partitions in the J-direction (lat axis).
!                    It must be equal to or greater than one.
!
! Nthreads         Number of PETs assigned to each model in the coupled system:
!
!                    Nthreads(roms)        ROMS PETs
!                    Nthreads(atmos)       Atmosphere model PETs
!                    Nthreads(seaice)      Sea-ice model PETs
!                    Nthreads(waves)       Wave model PETs
!
!                    Only the PET values for IsActive(...)=T are processed and
!                    considered.
!
!                    If sequential layout (PETlayoutOption=0), assign the same
!                    number of PETs to all coupled models.
!
!                    If concurrent layout (PETlayoutOption=1), the number of
!                    PETs needed is the sum of all active coupled model
!                    components.
!
! DebugLevel       Coupling debugging flag:
!
!                    [0] no debugging
!                    [1] reports informative messages
!                    [2] <1> and coupled components RunSequence
!                    [3] <2> and writes exchanged fields into NetCDF files
!                    [4] <3> and writes grid information in VTK format
!
!                    If DebugLevel >= 3, a single NetCDF file is created for
!                    every exchanged field when the "debug_write" switch in
!                    the Import/Export metadata file CPL_name is .TRUE. for
!                    field.  WARNING: Lot and lots of small NetCDF files are
!                    created that will slow down computations and fill disk
!                    space. Use this capability only during debugging to
!                    ensure that the regridding is doing correctly.
!
! TraceLevel       Execution tracing flag:
!
!                    [0] no tracing
!                    [1] reports the sequence of coupling subroutine calls
!                    [2] <1> plus writes voluminous ESMF library tracing
!                            information which slowdown performace, and
!                            creates large log file
!
!------------------------------------------------------------------------------
! ESMF library internal parameters.
!------------------------------------------------------------------------------
!
! extrapNumLevels  The number of levels for creep fill extrapolation of
!                    unmapped destination points during regridding. After
!                    regular regridding, the creep fill method of unmapped
!                    points repeatedly moves data from mapped locations to
!                    the neighboring unmapped locations by the specified
!                    "extrapNumLevels" amount. For each creeped point, the
!                    extrapolation value is the average of the immediate
!                    neighbors from regridding. See ESMF Reference Manual
!                    (version 8.0 or higher) for ESMF_EXTRAPMETHOD_CREEP
!                    flag to ESMF_FieldRegridStore.
!
!------------------------------------------------------------------------------
! Melding weighting coefficients.
!------------------------------------------------------------------------------
!
! Melding coefficients used to combine fields from DATA and ESM components.
! The weight factors are read from the input NetCDF specified in the
! "WeightsFile(atmos)"  keyword.  The user has full control of how the
! merging is done. It is recommended to provide a gradual transition between
! the two components.  The weighting factors have the same dimension as
! the atmosphere grid.
!
! Recall that the DATA component supplies needed data to a particular ESM
! component.  For example, it may export data to the atmosphere model at
! locations not covered by the other ESM components because of smaller grid
! coverage.  If the atmosphere and ocean model grids are incongruent, the
! atmosphere component needs to import sea surface temperature (SST) on
! those grid points not covered by the ocean component. Thus, the weighting
! coefficients are used to merge the SST data:
!
!   SST_atm(:,:) = Cesm(:,:) * SST_esm(;,;) + Cdat(:,:) * SST_dat(:,:)
!
! where Cesm(:,:) + Cdat(:,:) = 1.
!
! Currently, only the atmosphere component needs the weigth coefficients
! when DATA_COUPLING is activated.
!
! WeightsFile(atmos) Input NetCDF containing weighting coefficients for the
!                    atmosphere grid.
!
! VnameDATA(atmos)   NetCDF variable name containing the weight factor (Cdat)
!                    for the DATA component.
!
! VnameESM(atmos)    NetCDF variable name containing the weight factor (Cesm)
!                    for the ESM component (usually ocean: ROMS).
!
! NestedGrid(atmos)  Atmosphere grid number needing a merged field from
!                    DATA and ESM components. Usually, NestedGrid=1, but it
!                    is possible to merge DATA and an ESM component in a
!                    particular Atmosphere component nested grid.
!
!------------------------------------------------------------------------------
! Time Managing.
!------------------------------------------------------------------------------
!
! The next four parameters are specified in terms of six integers vector as:
! [YYYY MM DD hh mm ss], where
!
!                    YYYY         year of the century (integer)
!                    MM           month of the year: 1 - 12 (integer)
!                    DD           day of the month (integer)
!                    hh           hour of the day: 1 - 23 (integer)
!                    mm           minutes of the hour: 1 - 59 (integer)
!                    ss           secound of the minute: 1 - 59 (integer)
!
! ReferenceTime    ESM driver reference time for simulation. Usually, the
!                    ESM component measure time in seconds or days since
!                    the reference date [YYYY MM DD hh mm ss]
!
! StartTime        ESM driver starting time of coupling simulation.
!                    [YYYY MM DD hh mm ss]
!
! RestartTime      ESM driver re-start time of coupling simulation. If not
!                    restarting, set RestartTime to the same value as
!                    StartTime.
!                    [YYYY MM DD hh mm ss]
!
! StartTime        ESM driver stopping time for coupling simulation.
!                    [YYYY MM DD hh mm ss]
!
! TimeStep         ESM driver coupling interval. Use the largest value for
!                    the field (Import/Export) exchange between activated
!                    ESM components.
!                    [YYYY MM DD hh mm ss]
!
! Calendar         ESM driver day calendar:
!
!                    gregorian       Gregorian Calendar (adapted Oct 15, 1528)
!                    year_360_day    360 days per year, 30 days per month
!
! TimeFrac         Coupling time interval fraction (INTEGER) from driver
!                    TimeStep indicating how often the exchange of information
!                    between ESM components occurs. That is, the coupling
!                    interval between ESM components is TimeStep/TimeFrac.
!                    If coupling nested grids, specify the respective number
!                    of values. Only active model components are processed
!                    and considered.
!
!                    Components connected to ROMS, [1:NgridsR] expected:
!
!                      TimeFrac(ATM2OCN)      atmosphere -> ROMS
!                      TimeFrac(ICE2OCN)      seaice -> ROMS
!                      TimeFrac(WAV2OCN)      wave -> ROMS
!                      TimeFrac(DAT2OCN)      DATA -> ROMS
!
!                    Components connected to ATMOSPHERE, [1:NgridsA] expected:
!
!                      TimeFrac(OCN2ATM)      ROMS -> atmosphere
!                      TimeFrac(ICE2ATM)      seaice -> atmosphere
!                      TimeFrac(WAV2ATM)      wave -> atmosphere
!                      TimeFrac(DAT2ATM)      DATA -> atmosphere
!
!                    Components connected to SEAICE, [1:NgridsI] expected:
!
!                      TimeFrac(ATM2ICE)      atmosphere -> seaice
!                      TimeFrac(OCN2ICE)      ROMS -> seaice
!                      TimeFrac(WAV2ICE)      wave -> seaice
!                      TimeFrac(DAT2ICE)      DATA -> seaice
!
!                    Components connected to WAVE, [1:NgridsW] expected:
!
!                      TimeFrac(ATM2WAV)      atmosphere -> wave coupling
!                      TimeFrac(ICE2WAV)      seaice -> wave
!                      TimeFrac(OCN2WAV)      ROMS -> wave
!                      TimeFrac(DAT2WAV)      DATA -> wave
!
!                    For example, if TimeStep = 15 minutes = 900 seconds and
!                    TimeFrac = 3, the coupling exchange between those two
!                    components is:
!
!                      TimeStep/TimeFrac = 5 minutes = 300 seconds
!
!                    I recommed always to think in terms of seconds.  If
!                    coupling OCN-ATM, you need to consider the time-step
!                    size (DT) of both models and physics to determine the
!                    values of TimeStep and TimeFrac.
!
!                    The ATM model requires smaller DT because of fluid
!                    associated radius of deformation (RD).  The ATM (air) has
!                    a much larger value (RD ~ 1000 km) while OCN (seawater)
!                    has a much smaller value (RD ~ 100 km).  Therefore, the
!                    dynamics of the ATM are faster and require smaller DT.
!                    Contrarily, the dynamics of the OCN is slower and can be
!                    resolved with larger DT.
!
!------------------------------------------------------------------------------
! Export/Import fields name codes to process.
!------------------------------------------------------------------------------
!
! Export/Import fields names (abbreviated string codes). Currently, the
! following fields below are processed. However, the list can be expanded
! easly.
!
! The full metadata for these fields is defined in "coupling_esmf.dat".
!
! Nimport(...)     Number of import fields per model, [1:Nmodels] KEYWORD
!                   entries are expected with the label code in parenthesis.
!                   Set to zero if no fields to import by a particular model.
!
!                   Nimport(ocean)  = ?             Ocean model
!                   Nimport(atmos)  = ?             Atmosphere model
!                   Nimport(seaice) = ?             Sea-ice model
!                   Nimport(waves)  = ?             Wave model
!
! Import(...)     Import fields codes per model, Nimport(...) string codes
!                   are expected. If Nimport(...) = 0, set Import = NONE.
!
! Nexport(...)    Number of export fields per model, [1:Nmodels] KEYWORD
!                   entries are expected with the label code in parenthesis.
!                   Set to zero if no fields to export by a particular model.
!
!                   Nexport(ocean)  = ?             Ocean model
!                   Nexport(atmos)  = ?             Atmosphere model
!                   Nexport(seaice) = ?             Sea-ice model
!                   Nexport(waves)  = ?             Wave model
!
! Export(...)     Export fields codes per model, Nexport(...) string codes
!                   are expected. If Nexport(...) = 0, set Export = NONE.
!
!==============================================================================
! DATA Model parameters.
!
! The USER has the option to enter a filename for each export field and
! further split data by time records into multiple files. If nested grids,
! the USER may provide a different set of filenames for each grid. The DATA
! model will scan the file list and will read the needed data from the first
! file in the list containing the export field. Therefore, the order of the
! filenames is critical.
!==============================================================================
!
! nDataExport(roms)      The number of fields to export from DATA Model to
!                          ROMS component. If zero value, exporting of fields
!                          to ROMS is not needed and the associated values in
!                          DataExport(roms) and DataFiles(roms) are ignored.
!
! nDataFiles(roms)       The number of NetCDF files that the DATA Model needs
!                          to process to export fields to ROMS.
!
! DataExport(roms)       Field short name string to read and export to ROMS
!                          component, nDataExport(roms) values are expected.
!
! DataFiles(roms)        NetCDF filenames containing the field to export to
!                          ROMS component, nDataFiles(roms) are expected.
!
! Currently, this feature is used to debug the Data Model since ROMS has its
! own data managing infrastructure via input NetCDF files.  It will be use
! in the future for hybrid nesting to get export open boundary data from
! larger scale ocean component.
!
!------------------------------------------------------------------------------
!
! nDataExport(atmos)     The number of fields to export from DATA Model to the
!                          Atmosphere Model component. If zero value, exporting
!                          of fields to Atmosphere Model is not needed and the
!                          associated values in  DataExport(atmos) and
!                          DataFiles(atmos) are ignored.
!
! nDataFiles(atmos)      The number of NetCDF files that the DATA Model needs
!                          to process to export fields to the Atmosphere Model
!                          component
!
! DataExport(atmos)      Field short name string to read and export to the
!                          Atmosphere Model component, nDataExport(atmos)
!                          values are expected.
!
! DataFiles(atmos)       NetCDF filenames containing the field to export to
!                          the Atmosphere Model component, nDataFiles(atmos)
!                          are expected.
!
! For Example, we may need sea surface temperature at atmosphere model grid
!              points not covered by ROMS application.
!
!      nDataExport(atmos) == 1
!
!       nDataFiles(atmos) == 1
!
!       DataExport(atmos) == SST
!
!         DataFiles(atmos) == sst_day1.nc |
!                             sst_day2.nc |
!                             sst_day3.nc
!
!------------------------------------------------------------------------------
!
! nDataExport(seaice)    The number of fields to export from DATA Model to the
!                          Sea Ice Model component. If zero value, exporting
!                          of fields to Sea Ice Model is not needed and the
!                          associated values in  DataExport(seaice) and
!                          DataFiles(seaice) are ignored.
!
! nDataFiles(seaice)     The number of NetCDF files that the DATA Model needs
!                          to process to export fields to the Sea Ice Model
!                          component.
!
! DataExport(seaice)     Field short name string to read and export to the
!                          Sea Ice Model component, nDataExport(seaice)
!                          values are expected.
!
! DataFiles(seaice)      NetCDF filenames containing the field to export to
!                          the Sea Ice Model component, nDataFiles(seaice)
!                          are expected.
!
! For Example, CICE does not have an input data infrastructure and need the
! atmospheric forcing fields when coupling only to th ROMS component.
!
!     nDataExport(seaice) == 9
!
!      nDataFiles(seaice) == 2
!
!      DataExport(seaice) == rhoa  \
!                            potT  \
!                            flw   \
!                            swvdr \
!                            swvdf \
!                            swidr \
!                            swidf \
!                            frain \
!                            fsnow
!
!       DataFiles(seaice) ==  cice_forcing1.nc \
!                             cice_forcing2.nc
!
!------------------------------------------------------------------------------
!
! nDataExport(waves)     The number of fields to export from DATA Model to the
!                          Wave Model component. If zero value, exporting of
!                          fields to Wave Model is not needed and the
!                          associated values in  DataExport(waves) and
!                          DataFiles(waves) are ignored.
!
! nDataFiles(waves)      The number of NetCDF files that the DATA Model needs
!                          to process to export fields to the Wave Model
!                          component.
!
! DataExport(waves)      Field short name string to read and export to the
!                          Wave Model component, nDataExport(waves)
!                          values are expected.
!
! DataFiles(waves)       NetCDF filenames containing the field to export to
!                          the Wave Model component, nDataFiles(waves)
!                          are expected.
!
! For Example, the wave model needs to get wind and wind stress component from
!              the DATA Model:
!
!      nDataExport(waves) == 4
!
!       nDataFiles(waves) == 2
!
!       DataExport(waves) == Uwind \
!                            Vwind \
!                            Ustr  \
!                            Vstr
!
!         DataFiles(waves) == wind_month1.nc   |
!                             wind_month2.nc   |
!                             wind_month3.nc   \
!                             stress_month1.nc |
!                             stress_month2.nc |
!                             stress_month3.nc
!
