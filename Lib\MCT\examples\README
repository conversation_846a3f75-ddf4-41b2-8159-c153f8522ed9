
Directories containing example programs showing 
the use of MCT.

simple/ - Multiple single-source file examples showing how to set 
          up MCTWorld, GSMaps and send/recv data in various two-component
          coupled configurations (sequential and concurrent).  Require
	  no input data.

climate_concur1/ - A small program demonstrating MCT features
  in a configuration which mimics part of a concurrently executing
  climate model.  Uses real climate model numerical grids. Requires
  the MCT/data directory.


climate_sequen1/ - A small program demonstrating MCT features
  in a configuration which mimics part of a sequentially executing
  climate model.  Uses real climate model numerical grids.  Requires
  the MCT/data directory


More examples will be available in future releases.
