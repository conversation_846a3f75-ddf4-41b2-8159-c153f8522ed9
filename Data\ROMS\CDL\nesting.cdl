netcdf nesting {
dimensions:
	Ngrids = 2 ;
	Ncontact = 2 ;
	Nweights = 4 ;
	datum = UNLIMITED ; (1 currently)
variables:
	int spherical ;
		spherical:long_name = "grid type logical switch" ;
		spherical:flag_values = 0, 1 ;
		spherical:flag_meanings = "Cartesian spherical" ;
	int Lm(Ngrids) ;
		Lm:long_name = "number of interior RHO-points in the I-direction" ;
	int Mm(Ngrids) ;
		Mm:long_name = "number of interior RHO-points in the J-direction" ;
	int coincident(Ngrids) ;
		coincident:long_name = "coincident donor and receiver grids logical switch" ;
		coincident:flag_values = 0, 1 ;
		coincident:flag_meanings = "false true" ;
	int composite(Ngrids) ;
		composite:long_name = "composite grid type logical switch" ;
		composite:flag_values = 0, 1 ;
		composite:flag_meanings = "false true" ;
	int mosaic(Ngrids) ;
		mosaic:long_name = "mosaic grid type logical switch" ;
		mosaic:flag_values = 0, 1 ;
		mosaic:flag_meanings = "false true" ;
	int refinement(Ngrids) ;
		refinement:long_name = "refinement grid type logical switch" ;
		refinement:flag_values = 0, 1 ;
		refinement:flag_meanings = "false true" ;
	int refine_factor(Ngrids) ;
		refine_factor:long_name = "refinement factor from donor grid" ;
	int interpolate(Ncontact) ;
		interpolate:long_name = "vertical interpolation at contact points logical switch" ;
		interpolate:flag_values = 0, 1 ;
		interpolate:flag_meanings = "false true" ;
	int donor_grid(Ncontact) ;
		donor_grid:long_name = "data donor grid number" ;
	int receiver_grid(Ncontact) ;
		receiver_grid:long_name = "data receiver grid number" ;
	int NstrR(Ncontact) ;
		NstrR:long_name = "starting contact RHO-point index in data vector" ;
	int NendR(Ncontact) ;
		NendR:long_name = "ending contact RHO-point index in data vector" ;
	int NstrU(Ncontact) ;
		NstrU:long_name = "starting contact U-point index in data vector" ;
	int NendU(Ncontact) ;
		NendU:long_name = "ending contact U-point index in data vector" ;
	int NstrV(Ncontact) ;
		NstrV:long_name = "starting contact V-point index in data vector" ;
	int NendV(Ncontact) ;
		NendV:long_name = "ending contact V-point index in data vector" ;
	int contact_region(datum) ;
		contact_region:long_name = "contact region number" ;
	int on_boundary(datum) ;
		on_boundary:long_name = "contact point on receiver grid physical boundary" ;
		on_boundary:flag_values = 0, 1, 2, 3, 4 ;
		on_boundary:flag_meanings = "other western southern eastern northern" ;
	int Idg(datum) ;
		Idg:long_name = "I-left index of donor cell containing contact point" ;
	int Jdg(datum) ;
		Jdg:long_name = "J-bottom index of donor cell containing contact point" ;
	int Irg(datum) ;
		Irg:long_name = "receiver grid I-index of contact point" ;
		Irg:coordinates = "Xrg Yrg" ;
	int Jrg(datum) ;
		Jrg:long_name = "receiver grid J-index of contact point" ;
		Jrg:coordinates = "Xrg Yrg" ;
	double Xrg(datum) ;
		Xrg:long_name = "X-location of receiver grid contact points" ;
		Xrg:units = "meter" ;
	double Yrg(datum) ;
		Yrg:long_name = "Y-location of receiver grid contact points" ;
		Yrg:units = "meter" ;
	double Hweight(datum, Nweights) ;
		Hweight:long_name = "horizontal interpolation weights" ;
		Hweight:coordinates = "Xrg Yrg" ;
	double h(datum) ;
		h:long_name = "bathymetry at RHO-points" ;
		h:units = "meter" ;
		h:coordinates = "Xrg Yrg" ;
		h:_FillValue = 1.e+37 ;
	double f(datum) ;
		f:long_name = "Coriolis parameter at RHO-points" ;
		f:units = "second-1" ;
		f:coordinates = "Xrg Yrg" ;
		f:_FillValue = 1.e+37 ;
	double pm(datum) ;
		pm:long_name = "curvilinear coordinate metric in XI" ;
		pm:units = "meter-1" ;
		pm:coordinates = "Xrg Yrg" ;
		pm:_FillValue = 1.e+37 ;
	double pn(datum) ;
		pn:long_name = "curvilinear coordinate metric in ETA" ;
		pn:units = "meter-1" ;
		pn:coordinates = "Xrg Yrg" ;
		pn:_FillValue = 1.e+37 ;
	double dndx(datum) ;
		dndx:long_name = "XI-derivative of inverse metric factor pn" ;
		dndx:units = "meter" ;
		dndx:coordinates = "Xrg Yrg" ;
		dndx:_FillValue = 1.e+37 ;
	double dmde(datum) ;
		dmde:long_name = "ETA-derivative of inverse metric factor pm" ;
		dmde:units = "meter" ;
		dmde:coordinates = "Xrg Yrg" ;
		dmde:_FillValue = 1.e+37 ;
	double angle(datum) ;
		angle:long_name = "angle between XI-axis and EAST" ;
		angle:units = "radians" ;
		angle:coordinates = "Xrg Yrg" ;
		angle:_FillValue = 1.e+37 ;
	double mask(datum) ;
		mask:long_name = "land-sea mask of contact points" ;
		mask:flag_values = 0., 1. ;
		mask:flag_meanings = "land water" ;
		mask:coordinates = "Xrg Yrg" ;

// global attributes:
		:type = "ROMS Nesting Contact Regions Data" ;
		:grid_files = " \n",
			"~/ocean/repository/TestCases/dogbone/Data/dogbone_grd_left.nc \n",
			"~/ocean/repository/TestCases/dogbone/Data/dogbone_grd_right.nc" ;
		:history = "Contact points created by contact.m and written by write_contact.m, Monday - March 11, 2013 - 9:30:0.0 AM" ;
}
