c-----------------------------------------------------------------------
c
c  Include this file to get timing statistics for the different parts
c  of the Arnoldi update iteration.  An easy way to initialize all the
c  timing information to zero at the beginning is by:
c
c     call sstats  <-- symmetric code
c     call sstatn  <-- nonsymmetric code
c     call cstatn  <-- complex code
c
c-----------------------------------------------------------------------
c
c  nopx   = total number of user OP*x operation
c  nbx    = total number of user B*x operation (same as copy when B = I)
c  nrorth = total number of reorthogonalization steps taken
c  nitref = total number of it. refinement steps in reorthogonalization
c  nrstrt = total number of restart steps
c
c-----------------------------------------------------------------------
c
c========================================================
c===  Common to both symmetric and nonsymmetric code  ===
c========================================================
c
c  tgetv0 = total time spent in generating starting vector and 
c           restarted vector for the Arnoldi sequence.
c  titref = total time spent in iterative refinement phase in SSAITR.
c  trvec  = total time spent in computing the Ritz vectors before exit.
c
c====================================
c===  Specific to symmetric code  ===
c====================================
c
c  tsaupd = total time spent in SSAUPD.
c  tsaup2 = total time spent in SSAUP2.
c  tsaitr = total time spent in the basic Arnoldi iteration loop,
c           including iterative refinement in SSAITR.
c  tseigt = total time spent in computing the tridiagonal eigenvalue
c           subproblem at each iteration.
c  tsgets = total time spent in getting the shifts at each iteration.
c  tsapps = total time spent in applying the shifts at each iteration.
c  tsconv = total time spent in convergence test at each iteration.
c
c=======================================
c===  Specific to nonsymmetric code  ===
c=======================================
c
c  tnaupd = total time spent in SNAUPD.
c  tnaup2 = total time spent in SNAUP2.
c  tnaitr = total time spent in the basic Arnoldi iteration loop,
c           including iterative refinement in SNAITR.
c  tneigh = total time spent in computing the Hessenberg eigenvalue
c           subproblem at each iteration.
c  tngets = total time spent in getting the shifts at each iteration.
c  tnapps = total time spent in applying the shifts at each iteration.
c  tnconv = total time spent in convergence test at each iteration.
c
c==================================
c===  Specific to complex code  ===
c==================================
c
c  tcaupd = total time spent in CNAUPD.
c  tcaup2 = total time spent in CNAUP2.
c  tcaitr = total time spent in the basic Arnoldi iteration loop,
c           including iterative refinement in CNAITR.
c  tceigh = total time spent in computing the Hessenberg eigenvalue
c           subproblem at each iteration.
c  tcgets = total time spent in getting the shifts at each iteration.
c  tcapps = total time spent in applying the shifts at each iteration.
c  tcconv = total time spent in convergence test at each iteration.
c
c==================
c=== User time  ===
c==================
c
c  tmvopx = total time spent in computing Y = OP * X
c  tmvbx  = total time spent in computing Y = B * X
c
c=======================================================================
c
