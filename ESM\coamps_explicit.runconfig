#
# svn $Id: coamps_explicit.runconfig 1054 2021-03-06 19:47:12Z arango $
#::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
# Copyright (c) 2002-2021 The ROMS/TOMS Group                           :::
#   Licensed under a MIT/X style license                                :::
#   See License_ROMS.txt                                                :::
#::::::::::::::::::::::::::::::::::::::::::::::::::::: Hernan G. Arango :::
#                                                                       :::
# DATA-COAMPS-ROMS semi-implicit ESMF coupling:                         :::
#                                                                       :::
# This ESMF configuration file includes the Run Sequence to couple      :::
# DATA, COAMPS, and ROMS components. All the components interact with   :::
# the same coupling time step. The connectors from ROMS to COAMPS and   :::
# COAMPS to ROMS are explicit. It is recommended to use the semi-       :::
# implicit RunSequence configuration to ensure conservation. Usually,   :::
# the timestep of the atmosphere kernel is smaller than that for the    :::
# ocean.                                                                :::
#                                                                       :::
# The User needs to check that the single coupling interval specified   :::
# here is the same as for the ROMS coupling standard input script       :::
# (keyword TimeStep).                                                   :::
#                                                                       :::
# It is not necessary to specify options to the connectors here for     :::
# the "remapMethod" since it is specified in the input coupling         :::
# metadata file (keyword CPLname) for each exchanged field.             :::
#                                                                       :::
# If the timeStep value (in seconds) coupling interval is a wildcard    :::
# (*) character used for a single outer time loop in the run sequence,  :::
# then the associated run clock is identical to the driver clock, which :::
# is set up in standard input configuration script "coupling_esmf.in":  :::
#                                                                       :::
#     @*                                                                :::
#       ...                                                             :::
#       ...                                                             :::
#     @                                                                 :::
#                                                                       :::
# Check NUOPC reference manual for more information (Chapter 3, API).   :::
#                                                                       :::
# The component label is specific to how is known in the ESMF driver.   :::
# It is set in subroutine allocate_esmf_esm, variable MODELS(:)%name.   :::
#                                                                       :::
#::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

# Indian Ocean Application

runSeq::
  @300                       # timeStep = 5 min interval, single time loop
    DATA -> COAMPS           # DATA to COAMPS connector
    DATA
    COAMPS -> ROMS           # COAMPS to ROMS connector
    ROMS -> COAMPS           # ROMS to COAMPS connector
    ROMS
    COAMPS
  @
::
