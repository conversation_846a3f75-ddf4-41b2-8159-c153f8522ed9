netcdf s4dvar_std_b {

dimensions:
        xi_rho = 56 ;
        eta_rho = 55 ;
        xi_u = 55 ;
        eta_u = 55 ;
        xi_v = 56 ;
        eta_v = 54 ;
        IorJ = 56 ;
        s_rho = 30 ;
        s_w = 31 ;
        boundary = 4 ;

        ocean_time = UNLIMITED ; // (0 currently)

variables:
        int spherical ;
                spherical:long_name = "grid type logical switch" ;
                spherical:flag_values = "0, 1" ;
                spherical:flag_meanings = "Cartesian spherical" ;
        int Vtransform ;
                Vtransform:long_name = "vertical terrain-following transformation equation" ;
        int Vstretching ;
                Vstretching:long_name = "vertical terrain-following stretching function" ;
	double theta_s ;
		theta_s:long_name = "S-coordinate surface control parameter" ;
	double theta_b ;
		theta_b:long_name = "S-coordinate bottom control parameter" ;
	double Tcline ;
		Tcline:long_name = "S-coordinate surface/bottom layer width" ;
		Tcline:units = "meter" ;
	double hc ;
		hc:long_name = "S-coordinate parameter, critical depth" ;
		hc:units = "meter" ;
	double s_rho(s_rho) ;
		s_rho:long_name = "S-coordinate at RHO-points" ;
		s_rho:valid_min = -1. ;
		s_rho:valid_max = 0. ;
                s_rho:positive = "up" ;
                s_rho:standard_name = "ocean_s_coordinate_g1" ;
                s_rho:formula_terms = "s: s_rho C: Cs_r eta: zeta depth: h depth_c: hc" ;
	double s_w(s_w) ;
		s_w:long_name = "S-coordinate at W-points" ;
		s_w:valid_min = -1. ;
		s_w:valid_max = 0. ;
                s_w:positive = "up" ;
                s_w:standard_name = "ocean_s_coordinate_g1" ;
                s_w:formula_terms = "s: s_w C: Cs_w eta: zeta depth: h depth_c: hc" ;
	double Cs_r(s_rho) ;
		Cs_r:long_name = "S-coordinate stretching curves at RHO-points" ;
		Cs_r:valid_min = -1. ;
		Cs_r:valid_max = 0. ;
	double Cs_w(s_w) ;
		Cs_w:long_name = "S-coordinate stretching curves at W-points" ;
		Cs_w:valid_min = -1. ;
		Cs_w:valid_max = 0. ;
        double h(eta_rho, xi_rho) ;
                h:long_name = "bathymetry at RHO-points" ;
                h:units = "meter" ;
                h:coordinates = "lon_rho lat_rho" ;
	double lon_rho(eta_rho, xi_rho) ;
		lon_rho:long_name = "longitude of RHO-points" ;
		lon_rho:units = "degree_east" ;
                lon_rho:standard_name = "longitude" ;
	double lat_rho(eta_rho, xi_rho) ;
		lat_rho:long_name = "latitude of RHO-points" ;
		lat_rho:units = "degree_north" ;
                lat_rho:standard_name = "latitude" ;
	double lon_u(eta_u, xi_u) ;
		lon_u:long_name = "longitude of U-points" ;
		lon_u:units = "degree_east" ;
                lon_u:standard_name = "longitude" ;
	double lat_u(eta_u, xi_u) ;
		lat_u:long_name = "latitude of U-points" ;
		lat_u:units = "degree_north" ;
                lat_u:standard_name = "latitude" ;
	double lon_v(eta_v, xi_v) ;
		lon_v:long_name = "longitude of V-points" ;
		lon_v:units = "degree_east" ;
                lon_v:standard_name = "longitude" ;
	double lat_v(eta_v, xi_v) ;
		lat_v:long_name = "latitude of V-points" ;
		lat_v:units = "degree_north" ;
                lat_v:standard_name = "latitude" ;
	double ocean_time(ocean_time) ;
		ocean_time:long_name = "time since initialization" ;
		ocean_time:units = "seconds since 1968-05-23 00:00:00 GMT" ;
		ocean_time:calendar = "gregorian" ;
	double zeta_obc(ocean_time, boundary, IorJ) ;
		zeta_obc:long_name = "free-surface open boundaries standard deviation" ;
		zeta_obc:units = "meter" ;
		zeta_obc:time = "ocean_time" ;
	double ubar_obc(ocean_time, boundary, IorJ) ;
		ubar_obc:long_name = "vertically integrated u-momentum component open boundaries standard deviation" ;
		ubar_obc:units = "meter second-1" ;
		ubar_obc:time = "ocean_time" ;
	double vbar_obc(ocean_time, boundary, IorJ) ;
		vbar_obc:long_name = "vertically integrated v-momentum component open boundaries standard deviation" ;
		vbar_obc:units = "meter second-1" ;
		vbar_obc:time = "ocean_time" ;
        double u_obc(ocean_time, boundary, s_rho, IorJ) ;
                u_obc:long_name = "u-momentum component open boundaries standard deviation" ;
                u_obc:units = "meter second-1" ;
                u_obc:time = "ocean_time" ;
                u_obc:coordinates = "x_u y_u" ;
        double v_obc(ocean_time, boundary, s_rho, IorJ) ;
                v_obc:long_name = "v-momentum component open boundaries standard deviation" ;
                v_obc:units = "meter second-1" ;
                v_obc:time = "ocean_time" ;
        double temp_obc(ocean_time, boundary, s_rho, IorJ) ;
                temp_obc:long_name = "potential temperature open boundaries standard deviation" ;
                temp_obc:units = "Celsius" ;
                temp_obc:time = "ocean_time" ;
        double salt_obc(ocean_time, boundary, s_rho, IorJ) ;
                salt_obc:long_name = "salinity open boundaries standard deviation" ;
                salt_obc:time = "ocean_time" ;

// global attributes:
		:type = "ROMS/TOMS 4DVAR open boundary conditions error covariance standard deviation" ;
		:Conventions = "CF-1.4" ;
		:boundary_index = "West=1, South=2, East=3, North=4" ;
		:title = "California Current System, 1/3 degree resolution (WC13)" ;
		:grd_file = "wc13_grd.nc" ;
		:history = "Monday - June 21, 2010 -  11:00:00 AM" ;
}
